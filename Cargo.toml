[package]
name = "screen_capture"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "capture"
path = "src/capture.rs"

[[bin]]
name = "action"
path = "src/action.rs"

[[bin]]
name = "ai_capture"
path = "src/ai_capture.rs"

[[bin]]
name = "ai_action"
path = "src/ai_action.rs"

[dependencies]
eframe = "0.29"
egui = "0.29"
windows = { version = "0.58", features = [
    "Win32_Foundation",
    "Win32_Graphics_Gdi",
    "Win32_UI_WindowsAndMessaging",
    "Win32_System_Memory",
    "Win32_System_Threading",
    "Win32_Graphics_Direct3D11",
    "Win32_Graphics_Direct3D",
    "Win32_Graphics_Dxgi",
    "Win32_Graphics_Dxgi_Common",
    "Win32_UI_Input_KeyboardAndMouse"
]}
rayon = "1.8"
parking_lot = "0.12"
serialport = "4.5"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"
anyhow = "1.0"
crossbeam-channel = "0.5"
image = "0.24"
aes = "0.8"
rand = "0.8"
base64 = "0.22"
tokio = { version = "1.0", features = ["full"] }
if-addrs = "0.10"
winapi = { version = "0.3", features = ["winuser"] }
toml = "0.8"

# AI/YOLO dependencies
ort = "2.0"  # ONNX Runtime for .onnx models
ndarray = "0.15"  # N-dimensional arrays for tensor operations

# Optional dependencies for other model formats
# candle-core = "0.6"  # Candle ML framework for .pt models
# candle-nn = "0.6"
# candle-transformers = "0.6"
# tch = "0.15"  # PyTorch bindings for .pt models
# opencv = "0.92"  # OpenCV for image processing

[profile.release]
opt-level = 3          # Maximum optimization
lto = true             # Link-time optimization
codegen-units = 1      # Better optimization at cost of compile time
panic = "abort"        # Reduce binary size
strip = true           # Remove debug symbols
