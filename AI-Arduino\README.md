# AI Arduino Controller

This Arduino sketch combines the functionality of both the Silence and arduino-contact systems to provide comprehensive AI-driven mouse control.

## Features

### 🖱️ Mouse Passthrough
- Full USB mouse passthrough functionality from the Silence system
- Preserves all mouse buttons (left, right, middle, prev, next)
- Maintains scroll wheel functionality
- Zero latency mouse movement when not actively controlling

### 🎯 AI Aim Assist
- Smooth aim assistance based on AI target detection
- Configurable movement speeds and offsets
- Distance-based activation thresholds
- Compatible with the Silence communication protocol

### 👻 Silent Aim
- Complete silent aim implementation from arduino-contact
- Moves to target, clicks, then returns to original position
- Handles large movements with proper chunking
- Maintains cursor position accuracy

### 🔫 Trigger Bot
- Automated clicking when AI detects valid targets
- Configurable confidence thresholds
- Minimal delay for responsive shooting

## Hardware Requirements

- Arduino Leonardo, Micro, or compatible board with native USB support
- USB Host Shield (for mouse passthrough functionality)
- USB mouse connected to the Host Shield

## Installation

1. Install required libraries in Arduino IDE:
   - USB Host Shield Library 2.0
   - HID Library (usually included with Arduino IDE)

2. Copy all files from this directory to your Arduino sketch folder:
   - `AI-Arduino.ino` (main sketch)
   - `hidcustom.h` (mouse report parser)
   - `ImprovedMouse.h` (enhanced mouse functionality)

3. Upload the sketch to your Arduino board

## Communication Protocol

The Arduino accepts several command types via Serial (115200 baud):

### Aim Assist Commands
```
[X_BYTE][Y_BYTE]movemouse\n
```
- X_BYTE, Y_BYTE: Movement coordinates (0-255, with 127 as center)
- Used for smooth aim assistance

### Silent Aim Commands
```
silent[X_OFFSET]:[Y_OFFSET]\n
```
- X_OFFSET, Y_OFFSET: Target offset from current position
- Automatically moves to target, clicks, and returns

### Trigger Bot Commands
```
mouseclick\n
```
- Simple left mouse click
- Used for trigger bot functionality

### Shoot Commands
```
shoot\n
```
- Alternative click command
- Compatible with arduino-contact protocol

### Legacy Movement Commands
```
[X_OFFSET]:[Y_OFFSET]\n
```
- Direct movement commands
- Includes jump limiting for large movements

## Configuration

The sketch includes several configurable parameters:

- `jump = 127`: Maximum movement per step for legacy commands
- Serial timeout: 10ms for responsive communication
- Baud rate: 115200 for high-speed communication

## Wiring

Connect your USB Host Shield according to the standard pinout:
- VCC to 5V
- GND to GND
- MOSI to pin 16 (MOSI)
- MISO to pin 14 (MISO)
- SCK to pin 15 (SCK)
- SS to pin 10

Connect your mouse to the USB Host Shield's USB port.

## Usage with AI System

This Arduino sketch is designed to work with the AI capture and action Rust applications:

1. The AI capture system detects targets and sends coordinates via UDP
2. The AI action system receives coordinates and sends appropriate commands to this Arduino
3. The Arduino executes the commands while maintaining mouse passthrough

## Troubleshooting

- **Mouse not working**: Check USB Host Shield connections and power
- **No response to commands**: Verify serial connection and baud rate (115200)
- **Jerky movement**: Ensure proper grounding and stable power supply
- **Commands not recognized**: Check command format and line endings (\n)

## Compatibility

This sketch maintains backward compatibility with:
- Original Silence system commands
- arduino-contact system commands
- Standard mouse passthrough functionality

The unified design allows seamless integration with existing AI detection systems while providing enhanced functionality for modern AI-driven gaming assistance.
