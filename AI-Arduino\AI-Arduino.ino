#ifdef dobogusinclude
#include <spi4teensy3.h>
#endif
#include <SPI.h>
#include <Mouse.h>
#include <hiduniversal.h>

#include "hidcustom.h"

// USB Host Shield setup for mouse passthrough
USB Usb;
USBHub Hub(&Usb);
HIDBoot<USB_HID_PROTOCOL_MOUSE> HidMouse(&Usb);

// Mouse report parser for passthrough functionality
MouseRptParser Prs;

// Communication buffers
byte bf[2];
signed char delta[3] = {0, 0, 0};

// Movement variables for silent aim
int dx, dy, dxn, dyn;
int index, num_size;
int jump = 127;

void MouseRptParser::Parse(USBHID *hid, bool is_rpt_id, uint8_t len, uint8_t *buf)
{
  MYMOUSEINFO *pmi = (MYMOUSEINFO *)buf;

  // Handle left mouse button
  if (CHECK_BIT(prevState.mouseInfo.buttons, MOUSE_LEFT) != CHECK_BIT(pmi->buttons, MOUSE_LEFT))
  {
    if (CHECK_BIT(pmi->buttons, MOUSE_LEFT))
      Mouse.press(MOUSE_LEFT);
    else
      Mouse.release(MOUSE_LEFT);
  }

  // Handle right mouse button
  if (CHECK_BIT(prevState.mouseInfo.buttons, MOUSE_RIGHT) != CHECK_BIT(pmi->buttons, MOUSE_RIGHT))
  {
    if (CHECK_BIT(pmi->buttons, MOUSE_RIGHT))
      Mouse.press(MOUSE_RIGHT);
    else
      Mouse.release(MOUSE_RIGHT);
  }

  // Handle middle mouse button
  if (CHECK_BIT(prevState.mouseInfo.buttons, MOUSE_MIDDLE) != CHECK_BIT(pmi->buttons, MOUSE_MIDDLE))
  {
    if (CHECK_BIT(pmi->buttons, MOUSE_MIDDLE))
      Mouse.press(MOUSE_MIDDLE);
    else
      Mouse.release(MOUSE_MIDDLE);
  }

  // Handle previous button (mouse button 4)
  if (CHECK_BIT(prevState.mouseInfo.buttons, MOUSE_PREV) != CHECK_BIT(pmi->buttons, MOUSE_PREV))
  {
    if (CHECK_BIT(pmi->buttons, MOUSE_PREV))
      Mouse.press(MOUSE_PREV);
    else
      Mouse.release(MOUSE_PREV);
  }

  // Handle next button (mouse button 5)
  if (CHECK_BIT(prevState.mouseInfo.buttons, MOUSE_NEXT) != CHECK_BIT(pmi->buttons, MOUSE_NEXT))
  {
    if (CHECK_BIT(pmi->buttons, MOUSE_NEXT))
      Mouse.press(MOUSE_NEXT);
    else
      Mouse.release(MOUSE_NEXT);
  }

  // Handle mouse movement
  if (pmi->dX || pmi->dY)
    OnMouseMove(pmi);

  // Handle scroll wheel
  if (pmi->wheel)
    OnWheelMove(pmi);

  prevState.bInfo[0] = buf[0];
}

void MouseRptParser::OnMouseMove(MYMOUSEINFO *mi)
{
  delta[0] = mi->dX;
  delta[1] = mi->dY;
}

void MouseRptParser::OnWheelMove(MYMOUSEINFO *mi)
{
  delta[2] = mi->wheel;
}

void setup()
{
  // Initialize serial communication
  Serial.begin(115200);
  Serial.setTimeout(10);
  Serial.println("AI Arduino Controller Started");
  Serial.println("Features: Mouse Passthrough + AI Aim Assist + Silent Aim + Trigger Bot");
  
  // Initialize USB Host Shield
  if (Usb.Init() == -1) {
    Serial.println("USB Host Shield initialization failed!");
  } else {
    Serial.println("USB Host Shield initialized successfully");
  }
  
  // Set up mouse report parser
  HidMouse.SetReportParser(0, &Prs);
  
  // Initialize mouse library
  Mouse.begin();
  
  Serial.println("Ready for AI commands...");
}

void loop()
{
  // Reset movement deltas
  delta[0] = 0;
  delta[1] = 0;
  delta[2] = 0;
  
  // Process USB tasks for mouse passthrough
  Usb.Task();

  // Check for serial commands from AI system
  if (Serial.available())
  {
    // Read coordinate bytes first (for Silence-style communication)
    Serial.readBytes(bf, 2);
    String message = Serial.readStringUntil('\n');
    message.trim();

    // Handle different command types
    if (message == "mouseclick") {
      // Simple mouse click (trigger bot)
      Mouse.click(MOUSE_LEFT);
      Serial.println("Trigger: Click executed");
    }
    else if (message == "movemouse") {
      // Aim assist movement (Silence-style)
      Mouse.move(bf[0], bf[1], 0);
      Serial.print("Aim Assist: Move ");
      Serial.print(bf[0]);
      Serial.print(", ");
      Serial.println(bf[1]);
    }
    else if (message == "shoot") {
      // Simple shoot command (from arduino-contact)
      Mouse.click();
      Serial.println("Shoot command executed");
    }
    else if (message.startsWith("silent")) {
      // Silent aim implementation (from arduino-contact)
      handleSilentAim(message);
    }
    else {
      // Legacy coordinate parsing (from arduino-contact)
      handleLegacyMovement(message);
    }
  }
  else { 
    // Pass through mouse movement when no serial commands
    Mouse.move(delta[0], delta[1], delta[2]);
  }
}

void handleSilentAim(String data) {
  Serial.println("Silent Aim: Processing command");
  
  // Remove "silent" prefix
  data.remove(0, 6);
  index = 0;
  num_size = data.indexOf(":", index);
  dx = data.substring(index, num_size).toInt();
  data.remove(0, num_size + 1);
  dy = data.toInt();
  dxn = dx * -1;
  dyn = dy * -1;

  Serial.print("Silent Aim: dx=");
  Serial.print(dx);
  Serial.print(", dy=");
  Serial.println(dy);

  // Move to target (X axis)
  if (dx > 0) {
    while (dx > 127) {
      dx -= 127;
      Mouse.move(127, 0);
    }
    Mouse.move(dx, 0);
  }
  else if (dx < 0) {
    while (dx < -127) {
      dx += 127;
      Mouse.move(-127, 0);
    }
    Mouse.move(dx, 0);
  }
  
  // Move to target (Y axis)
  if (dy > 0) {
    while (dy > 127) {
      dy -= 127;
      Mouse.move(0, 127);
    }
    Mouse.move(0, dy);
  }
  else if (dy < 0) {
    while (dy < -127) {
      dy += 127;
      Mouse.move(0, -127);
    }
    Mouse.move(0, dy);
  }
  
  // Click at target
  Mouse.click();
  Serial.println("Silent Aim: Target clicked");
  
  // Move back to original position (X axis)
  if (dxn > 0) {
    while (dxn > 127) {
      dxn -= 127;
      Mouse.move(127, 0);
    }
    Mouse.move(dxn, 0);
  }
  else if (dxn < 0) {
    while (dxn < -127) {
      dxn += 127;
      Mouse.move(-127, 0);
    }
    Mouse.move(dxn, 0);
  }
  
  // Move back to original position (Y axis)
  if (dyn > 0) {
    while (dyn > 127) {
      dyn -= 127;
      Mouse.move(0, 127);
    }
    Mouse.move(0, dyn);
  }
  else if (dyn < 0) {
    while (dyn < -127) {
      dyn += 127;
      Mouse.move(0, -127);
    }
    Mouse.move(0, dyn);
  }
  
  Serial.println("Silent Aim: Returned to original position");
}

void handleLegacyMovement(String data) {
  // Legacy coordinate parsing for backward compatibility
  index = 0;
  num_size = data.indexOf(":", index);
  if (num_size > 0) {
    dx = data.substring(index, num_size).toInt();
    data.remove(0, num_size + 1);
    dy = data.toInt();

    Serial.print("Legacy Move: dx=");
    Serial.print(dx);
    Serial.print(", dy=");
    Serial.println(dy);

    // Move with jump limiting (X axis)
    if (dx > 0) {
      while (dx > jump) {
        dx -= jump;
        Mouse.move(jump, 0);
      }
      Mouse.move(dx, 0);
    }
    else if (dx < 0) {
      while (dx < -jump) {
        dx += jump;
        Mouse.move(-jump, 0);
      }
      Mouse.move(dx, 0);
    }
    
    // Move with jump limiting (Y axis)
    if (dy > 0) {
      while (dy > jump) {
        dy -= jump;
        Mouse.move(0, jump);
      }
      Mouse.move(0, dy);
    }
    else if (dy < 0) {
      while (dy < -jump) {
        dy += jump;
        Mouse.move(0, -jump);
      }
      Mouse.move(0, dy);
    }
  }
}
