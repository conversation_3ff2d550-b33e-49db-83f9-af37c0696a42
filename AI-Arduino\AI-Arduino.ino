/*
 * AI Arduino Controller
 * Simplified version focusing on AI aim assist, silent aim, and trigger bot
 * Compatible with both Silence and arduino-contact protocols
 */

#include <Mouse.h>

// Communication buffers
byte bf[2];

// Movement variables for silent aim
int dx, dy, dxn, dyn;
int index, num_size;
int jump = 127;

// Status LED pin (optional)
const int LED_PIN = 13;

void setup()
{
  // Initialize serial communication
  Serial.begin(115200);
  Serial.setTimeout(10);
  Serial.println("AI Arduino Controller Started");
  Serial.println("Features: AI Aim Assist + Silent Aim + Trigger Bot");

  // Initialize mouse library
  Mouse.begin();

  // Initialize LED pin
  pinMode(LED_PIN, OUTPUT);
  digitalWrite(LED_PIN, HIGH); // Turn on LED to indicate ready

  Serial.println("Ready for AI commands...");
}

void loop()
{
  // Check for serial commands from AI system
  if (Serial.available())
  {
    // Read coordinate bytes first (for Silence-style communication)
    Serial.readBytes(bf, 2);
    String message = Serial.readStringUntil('\n');
    message.trim();

    // Blink LED to show activity
    digitalWrite(LED_PIN, LOW);
    delay(1);
    digitalWrite(LED_PIN, HIGH);

    // Handle different command types
    if (message == "mouseclick") {
      // Simple mouse click (trigger bot)
      Mouse.click(MOUSE_LEFT);
      Serial.println("Trigger: Click executed");
    }
    else if (message == "movemouse") {
      // Aim assist movement (Silence-style)
      // Convert unsigned bytes to signed movement
      int8_t x_move = (int8_t)bf[0];
      int8_t y_move = (int8_t)bf[1];
      Mouse.move(x_move, y_move, 0);
      Serial.print("Aim Assist: Move ");
      Serial.print(x_move);
      Serial.print(", ");
      Serial.println(y_move);
    }
    else if (message == "shoot") {
      // Simple shoot command (from arduino-contact)
      Mouse.click();
      Serial.println("Shoot command executed");
    }
    else if (message.startsWith("silent")) {
      // Silent aim implementation (from arduino-contact)
      handleSilentAim(message);
    }
    else {
      // Legacy coordinate parsing (from arduino-contact)
      handleLegacyMovement(message);
    }
  }
}

void handleSilentAim(String data) {
  Serial.println("Silent Aim: Processing command");

  // Remove "silent" prefix
  data.remove(0, 6);
  index = 0;
  num_size = data.indexOf(":", index);
  dx = data.substring(index, num_size).toInt();
  data.remove(0, num_size + 1);
  dy = data.toInt();
  dxn = dx * -1;
  dyn = dy * -1;

  Serial.print("Silent Aim: dx=");
  Serial.print(dx);
  Serial.print(", dy=");
  Serial.println(dy);

  // Move to target (X axis)
  if (dx > 0) {
    while (dx > 127) {
      dx -= 127;
      Mouse.move(127, 0);
    }
    Mouse.move(dx, 0);
  }
  else if (dx < 0) {
    while (dx < -127) {
      dx += 127;
      Mouse.move(-127, 0);
    }
    Mouse.move(dx, 0);
  }

  // Move to target (Y axis)
  if (dy > 0) {
    while (dy > 127) {
      dy -= 127;
      Mouse.move(0, 127);
    }
    Mouse.move(0, dy);
  }
  else if (dy < 0) {
    while (dy < -127) {
      dy += 127;
      Mouse.move(0, -127);
    }
    Mouse.move(0, dy);
  }

  // Click at target
  Mouse.click();
  Serial.println("Silent Aim: Target clicked");

  // Move back to original position (X axis)
  if (dxn > 0) {
    while (dxn > 127) {
      dxn -= 127;
      Mouse.move(127, 0);
    }
    Mouse.move(dxn, 0);
  }
  else if (dxn < 0) {
    while (dxn < -127) {
      dxn += 127;
      Mouse.move(-127, 0);
    }
    Mouse.move(dxn, 0);
  }

  // Move back to original position (Y axis)
  if (dyn > 0) {
    while (dyn > 127) {
      dyn -= 127;
      Mouse.move(0, 127);
    }
    Mouse.move(0, dyn);
  }
  else if (dyn < 0) {
    while (dyn < -127) {
      dyn += 127;
      Mouse.move(0, -127);
    }
    Mouse.move(0, dyn);
  }

  Serial.println("Silent Aim: Returned to original position");
}

void handleLegacyMovement(String data) {
  // Legacy coordinate parsing for backward compatibility
  index = 0;
  num_size = data.indexOf(":", index);
  if (num_size > 0) {
    dx = data.substring(index, num_size).toInt();
    data.remove(0, num_size + 1);
    dy = data.toInt();

    Serial.print("Legacy Move: dx=");
    Serial.print(dx);
    Serial.print(", dy=");
    Serial.println(dy);

    // Move with jump limiting (X axis)
    if (dx > 0) {
      while (dx > jump) {
        dx -= jump;
        Mouse.move(jump, 0);
      }
      Mouse.move(dx, 0);
    }
    else if (dx < 0) {
      while (dx < -jump) {
        dx += jump;
        Mouse.move(-jump, 0);
      }
      Mouse.move(dx, 0);
    }

    // Move with jump limiting (Y axis)
    if (dy > 0) {
      while (dy > jump) {
        dy -= jump;
        Mouse.move(0, jump);
      }
      Mouse.move(0, dy);
    }
    else if (dy < 0) {
      while (dy < -jump) {
        dy += jump;
        Mouse.move(0, -jump);
      }
      Mouse.move(0, dy);
    }
  }
}
