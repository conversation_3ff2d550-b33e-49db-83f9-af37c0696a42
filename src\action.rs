#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use eframe::egui;
use parking_lot::Mutex;
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use std::thread;
use std::time::{Duration, Instant};
use std::net::UdpSocket;
use windows::Win32::UI::Input::KeyboardAndMouse::*;
use serialport::{SerialPort, available_ports};
use std::io::Write;
use serde::{Deserialize, Serialize};
use serde_json;
use anyhow::{Result, Context};
use aes::Aes256;
use aes::cipher::{BlockEncrypt, BlockDecrypt, KeyInit, generic_array::GenericArray};
use rand::RngCore;
use base64::{Engine as _, engine::general_purpose};

mod network_discovery;
use network_discovery::{NetworkDiscovery, DeviceType, ConnectionStatus};

// Default encryption key (must match capture.rs)
const DEFAULT_ENCRYPTION_KEY: &str = "SilenceCapture2024KeyForSecureUDP!!";

// Encryption utilities (copy from capture.rs)
struct CryptoUtils {
    cipher: Option<Aes256>,
}

impl CryptoUtils {
    fn new() -> Self {
        Self { cipher: None }
    }
    
    fn get_or_create_cipher(&mut self, key: &str) -> &Aes256 {
        if self.cipher.is_none() {
            let mut key_bytes = [0u8; 32];
            let key_data = key.as_bytes();
            let copy_len = key_data.len().min(32);
            key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
            self.cipher = Some(Aes256::new(GenericArray::from_slice(&key_bytes)));
        }
        self.cipher.as_ref().unwrap()
    }
    // Pad data to AES block size (16 bytes)
    #[allow(dead_code)]
    fn pad_data(data: &[u8]) -> Vec<u8> {
        let mut padded = data.to_vec();
        let padding_needed = 16 - (data.len() % 16);
        if padding_needed != 16 {
            padded.extend(vec![padding_needed as u8; padding_needed]);
        }
        padded
    }
    
    // Remove padding from decrypted data
    fn unpad_data(data: &[u8]) -> Result<Vec<u8>> {
        if data.is_empty() {
            return Err(anyhow::anyhow!("Empty data cannot be unpadded"));
        }
        
        let padding_size = *data.last().unwrap() as usize;
        if padding_size == 0 || padding_size > 16 || padding_size > data.len() {
            return Err(anyhow::anyhow!("Invalid padding"));
        }
        
        // Verify padding bytes
        for &byte in &data[data.len() - padding_size..] {
            if byte != padding_size as u8 {
                return Err(anyhow::anyhow!("Invalid padding bytes"));
            }
        }
        
        Ok(data[..data.len() - padding_size].to_vec())
    }
      // Encrypt data using AES-256
    #[allow(dead_code)]
    fn encrypt(data: &str, key: &str) -> Result<String> {
        // Create 256-bit key from provided key string
        let mut key_bytes = [0u8; 32];
        let key_data = key.as_bytes();
        let copy_len = key_data.len().min(32);
        key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
        
        let cipher = Aes256::new(GenericArray::from_slice(&key_bytes));
        let padded_data = Self::pad_data(data.as_bytes());
        
        let mut encrypted = Vec::new();
        
        // Encrypt in 16-byte blocks
        for chunk in padded_data.chunks(16) {
            let mut block = GenericArray::clone_from_slice(chunk);
            cipher.encrypt_block(&mut block);
            encrypted.extend_from_slice(&block);
        }
        
        // Generate random IV (16 bytes)
        let mut iv = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut iv);
        
        // Prepend IV to encrypted data
        let mut result = iv.to_vec();
        result.extend(encrypted);
        
        // Encode as base64
        Ok(general_purpose::STANDARD.encode(&result))
    }
    
    // Fast decrypt method that reuses cipher
    fn fast_decrypt(&mut self, encrypted_data: &str, key: &str) -> Result<String> {
        // Decode from base64
        let data = general_purpose::STANDARD.decode(encrypted_data)
            .context("Failed to decode base64")?;
        
        if data.len() < 32 { // At least IV (16) + one block (16)
            return Err(anyhow::anyhow!("Encrypted data too short"));
        }
        
        let cipher = self.get_or_create_cipher(key);
        
        // Extract IV and encrypted data
        let _iv = &data[0..16]; // IV for future use if needed
        let encrypted = &data[16..];
        
        let mut decrypted = Vec::with_capacity(encrypted.len());
        
        // Decrypt in 16-byte blocks
        for chunk in encrypted.chunks(16) {
            if chunk.len() == 16 {
                let mut block = GenericArray::clone_from_slice(chunk);
                cipher.decrypt_block(&mut block);
                decrypted.extend_from_slice(&block);
            }
        }
        
        // Remove padding
        let unpadded = Self::unpad_data(&decrypted)?;
        
        // Convert to string
        String::from_utf8(unpadded).context("Failed to convert decrypted data to string")
    }
    
    // Decrypt data using AES-256
    fn decrypt(encrypted_data: &str, key: &str) -> Result<String> {
        // Decode from base64
        let data = general_purpose::STANDARD.decode(encrypted_data)
            .context("Failed to decode base64")?;
        
        if data.len() < 32 { // At least IV (16) + one block (16)
            return Err(anyhow::anyhow!("Encrypted data too short"));
        }
        
        // Create 256-bit key from provided key string
        let mut key_bytes = [0u8; 32];
        let key_data = key.as_bytes();
        let copy_len = key_data.len().min(32);
        key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
        
        let cipher = Aes256::new(GenericArray::from_slice(&key_bytes));
        
        // Extract IV and encrypted data
        let _iv = &data[0..16]; // IV for future use if needed
        let encrypted = &data[16..];
        
        let mut decrypted = Vec::new();
        
        // Decrypt in 16-byte blocks
        for chunk in encrypted.chunks(16) {
            if chunk.len() == 16 {
                let mut block = GenericArray::clone_from_slice(chunk);
                cipher.decrypt_block(&mut block);
                decrypted.extend_from_slice(&block);
            }
        }
        
        // Remove padding
        let unpadded = Self::unpad_data(&decrypted)?;
        
        // Convert to string
        String::from_utf8(unpadded).context("Failed to convert decrypted data to string")
    }
}

// UDP Message structure for receiving target data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TargetMessage {
    pub center_x: f32,
    pub center_y: f32,
    pub confidence: f32,
    pub capture_size: u32,
    pub message_type: String, // "aim", "trigger", or "clear"
    pub timestamp: u64, // Timestamp in milliseconds
}

// Configuration structures for action controls
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ActionConfig {
    pub com_port: String,
    pub aim_assist: bool,
    pub trigger_bot: bool,
    pub aim_key: String,
    pub trigger_key: u32,
    pub aim_speed_x: f32,
    pub aim_speed_y: f32,
    pub aim_offset: i32,
    pub aim_offset_x: i32,
    pub aim_fps: u32,
    pub trigger_delay: f32,
    pub trigger_fps: u32,
    pub udp_listen_port: u16,
    pub enable_encryption: bool,
    pub encryption_key: String,
}

impl Default for ActionConfig {
    fn default() -> Self {
        Self {
            com_port: "auto".to_string(),
            aim_assist: false,
            trigger_bot: false,
            aim_key: "auto".to_string(),
            trigger_key: 0x05, // Right mouse button
            aim_speed_x: 0.6,
            aim_speed_y: 0.3,
            aim_offset: 8,
            aim_offset_x: 0,
            aim_fps: 285,
            trigger_delay: 0.2,
            trigger_fps: 285,
            udp_listen_port: 12345,
            enable_encryption: true,
            encryption_key: DEFAULT_ENCRYPTION_KEY.to_string(),
        }
    }
}

impl ActionConfig {
    pub fn load_from_file() -> Self {
        match std::fs::read_to_string("action_config.toml") {
            Ok(content) => {
                match toml::from_str::<ActionConfig>(&content) {
                    Ok(config) => config,
                    Err(_) => Self::default()
                }
            }
            Err(_) => Self::default()
        }
    }

    pub fn save_to_file(&self) -> Result<()> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write("action_config.toml", content)?;
        Ok(())
    }
}

// Arduino communication
pub struct ArduinoController {
    port: Box<dyn SerialPort>,
}

impl ArduinoController {    pub fn new() -> Result<Self> {
        let port_name = Self::detect_arduino_port()?;
        let port = serialport::new(&port_name, 115200)
            .timeout(Duration::from_millis(100))
            .open()
            .context("Failed to open Arduino port")?;
        Ok(Self { port })
    }
    
    pub fn with_port(port_name: &str) -> Result<Self> {
        let port = serialport::new(port_name, 115200)
            .timeout(Duration::from_millis(100))
            .open()
            .context("Failed to open Arduino port")?;
        Ok(Self { port })
    }    fn detect_arduino_port() -> Result<String> {
        let ports = available_ports().context("Failed to list serial ports")?;
        
        for port in ports {
            let port_name_lower = port.port_name.to_lowercase();
            let has_arduino_name = port_name_lower.contains("arduino") || port_name_lower.contains("com");
            
            let is_usb_serial = match &port.port_type {
                serialport::SerialPortType::UsbPort(usb_info) => {
                    let desc = format!("{:?}", usb_info).to_lowercase();
                    desc.contains("arduino") || desc.contains("usb") || desc.contains("ch340") || desc.contains("cp210")
                }
                _ => false,
            };
            
            if has_arduino_name || is_usb_serial {
                return Ok(port.port_name);
            }
        }
        
        anyhow::bail!("No Arduino found")
    }
    
    pub fn get_available_ports() -> Result<Vec<String>> {
        let ports = available_ports().context("Failed to list serial ports")?;
        Ok(ports.into_iter().map(|p| p.port_name).collect())
    }    pub fn mousemove_aim(&mut self, x: i32, y: i32, message: &str) -> Result<()> {
        let mut x_coord = if x < 0 { x + 256 } else { x };
        let mut y_coord = if y < 0 { y + 256 } else { y };
        
        x_coord = x_coord.max(0).min(255);
        y_coord = y_coord.max(0).min(255);
        
        let coord_bytes = [x_coord as u8, y_coord as u8];
        let message_bytes = format!("{}\n", message).into_bytes();
        
        self.port.write_all(&coord_bytes)
            .context("Failed to write coordinates to Arduino")?;
        self.port.write_all(&message_bytes)
            .context("Failed to write message to Arduino")?;
        
        Ok(())
    }    pub fn mouse_click(&mut self) -> Result<()> {
        self.mousemove_aim(0, 0, "mouseclick")
    }
}

// Key monitoring and action processing
pub struct ActionProcessor {
    config: Arc<Mutex<ActionConfig>>,
    arduino: Arc<Mutex<Option<ArduinoController>>>,
    #[allow(dead_code)]
    last_aim_time: Instant,
    #[allow(dead_code)]
    last_trigger_time: Instant,
    udp_socket: UdpSocket,
    last_target_data: Arc<Mutex<Option<TargetMessage>>>,
}

impl ActionProcessor {    pub fn new(config: Arc<Mutex<ActionConfig>>) -> Result<Self> {
        let listen_port = config.lock().udp_listen_port;
        let socket = UdpSocket::bind(format!("0.0.0.0:{}", listen_port))?;
        socket.set_nonblocking(true)?;
        
        let arduino = if config.lock().com_port == "auto" {
            Arc::new(Mutex::new(ArduinoController::new().ok()))
        } else {
            let port_name = config.lock().com_port.clone();
            Arc::new(Mutex::new(ArduinoController::with_port(&port_name).ok()))
        };
        
        Ok(Self {
            config,
            arduino,
            last_aim_time: Instant::now(),
            last_trigger_time: Instant::now(),
            udp_socket: socket,
            last_target_data: Arc::new(Mutex::new(None)),
        })
    }
    
    pub fn reconnect_arduino(&self) -> Result<()> {
        let config = self.config.lock();
        let new_arduino = if config.com_port == "auto" {
            ArduinoController::new()?
        } else {
            ArduinoController::with_port(&config.com_port)?
        };
        *self.arduino.lock() = Some(new_arduino);
        Ok(())
    }
    
    pub fn is_arduino_connected(&self) -> bool {
        self.arduino.lock().is_some()
    }
    
    pub fn start_processing(&self) -> Arc<AtomicBool> {
        let is_running = Arc::new(AtomicBool::new(true));
          
        // Start UDP listener thread
        let udp_socket = self.udp_socket.try_clone().unwrap();
        let target_data = self.last_target_data.clone();
        let config_clone = self.config.clone();
        let running_clone = is_running.clone();
        
        thread::spawn(move || {
            let mut buffer = [0u8; 2048]; // Increased buffer size
            let mut crypto_utils = CryptoUtils::new(); // Reusable crypto instance
            
            while running_clone.load(Ordering::Relaxed) {
                match udp_socket.recv(&mut buffer) {                    
                    Ok(size) => {
                        let receive_time = std::time::SystemTime::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_millis() as u64;
                            
                        let received_data = &buffer[..size];
                        
                        // Get config values once and drop lock quickly
                        let (enable_encryption, encryption_key) = {
                            let config = config_clone.lock();
                            (config.enable_encryption, config.encryption_key.clone())
                        };
                        
                        // Try to decrypt the message if encryption is enabled
                        let message_str = if enable_encryption {
                            // Convert bytes to string first for base64 decoding
                            let encrypted_string = match std::str::from_utf8(received_data) {
                                Ok(s) => s,
                                Err(_) => continue,
                            };
                            
                            match crypto_utils.fast_decrypt(encrypted_string, &encryption_key) {
                                Ok(decrypted) => decrypted,
                                Err(_) => continue, // Skip this message
                            }
                        } else {
                            // Plain text mode
                            match std::str::from_utf8(received_data) {
                                Ok(s) => s.to_string(),
                                Err(_) => continue,
                            }
                        };
                        
                        // Parse the decrypted/plain text message
                        if let Ok(target_msg) = serde_json::from_str::<TargetMessage>(&message_str) {
                            // Log timing information for debugging
                            let processing_delay = receive_time.saturating_sub(target_msg.timestamp);
                            if processing_delay > 100 {
                                eprintln!("High latency detected: {}ms (msg: {} -> recv: {})", 
                                    processing_delay, target_msg.timestamp, receive_time);
                            }
                            
                            if target_msg.message_type == "clear" {
                                *target_data.lock() = None;
                            } else {
                                *target_data.lock() = Some(target_msg);
                            }
                        }
                    }
                    Err(_) => {
                        // Reduced sleep time for faster response
                        thread::sleep(Duration::from_micros(100));
                    }
                }
            }
        });
        
        // Start action processing thread
        let config_clone = self.config.clone();
        let arduino_clone = self.arduino.clone();
        let target_data_clone = self.last_target_data.clone();
        let running_clone2 = is_running.clone();
        
        thread::spawn(move || {
            let mut last_aim_time = Instant::now();
            let mut last_trigger_time = Instant::now();
            
            while running_clone2.load(Ordering::Relaxed) {
                let config = config_clone.lock().clone();
                
                // Process aim assist
                if config.aim_assist {
                    let aim_sleep = Duration::from_secs_f32(1.0 / config.aim_fps as f32);
                    if last_aim_time.elapsed() >= aim_sleep {
                        Self::process_aim_assist_static(&config, &arduino_clone, &target_data_clone);
                        last_aim_time = Instant::now();
                    }
                }
                
                // Process trigger bot
                if config.trigger_bot {
                    let trigger_sleep = Duration::from_secs_f32(1.0 / config.trigger_fps as f32);
                    if last_trigger_time.elapsed() >= trigger_sleep {
                        Self::process_trigger_bot_static(&config, &arduino_clone, &target_data_clone);
                        last_trigger_time = Instant::now();
                    }
                }
                
                thread::sleep(Duration::from_micros(50));
            }
        });
        
        is_running
    }    fn process_aim_assist_static(
        config: &ActionConfig,
        arduino: &Arc<Mutex<Option<ArduinoController>>>,
        target_data: &Arc<Mutex<Option<TargetMessage>>>,
    ) {
        let should_aim = config.aim_key == "auto" || unsafe {
            if let Ok(key_code) = u32::from_str_radix(&config.aim_key.trim_start_matches("0x"), 16) {
                GetAsyncKeyState(key_code as i32) < 0
            } else {
                false
            }
        };
        
        if !should_aim {
            return;
        }
        
        if let Some(target_msg) = target_data.lock().as_ref() {
            if target_msg.message_type == "aim" {
                // Check if the target data is recent (within 1500ms)
                let current_time = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_millis() as u64;
                
                let time_diff = if current_time >= target_msg.timestamp {
                    current_time - target_msg.timestamp
                } else {
                    0
                };
                
                if time_diff > 1500 { // Increased from 500ms to handle current latency
                    return;
                }
                
                let fov_center = target_msg.capture_size as f32 / 2.0;
                let x_offset = target_msg.center_x - fov_center;
                let y_offset = target_msg.center_y - fov_center;
                
                let adjusted_y = y_offset - config.aim_offset as f32;
                let adjusted_x = x_offset - config.aim_offset_x as f32;
                
                let x_move = (adjusted_x * config.aim_speed_x) as i32;
                let y_move = (adjusted_y * config.aim_speed_y) as i32;
                
                if let Some(ref mut arduino_controller) = arduino.lock().as_mut() {
                    let _ = arduino_controller.mousemove_aim(x_move, y_move, "movemouse");
                }
            }
        }
    }    fn process_trigger_bot_static(
        config: &ActionConfig,
        arduino: &Arc<Mutex<Option<ArduinoController>>>,
        target_data: &Arc<Mutex<Option<TargetMessage>>>,
    ) {
        let trigger_pressed = unsafe {
            GetAsyncKeyState(config.trigger_key as i32) < 0
        };
        
        if !trigger_pressed {
            return;
        }
        
        if let Some(target_msg) = target_data.lock().as_ref() {
            if target_msg.message_type == "trigger" {
                // Check if the target data is recent (within 1500ms)
                let current_time = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_millis() as u64;
                
                let time_diff = if current_time >= target_msg.timestamp {
                    current_time - target_msg.timestamp
                } else {
                    0
                };
                
                if time_diff > 1500 { // Increased from 500ms to handle current latency
                    return;
                }
                
                std::thread::sleep(Duration::from_secs_f32(config.trigger_delay));
                
                if let Some(ref mut arduino_controller) = arduino.lock().as_mut() {
                    let _ = arduino_controller.mouse_click();
                }
            }
        }
    }
    
    pub fn get_last_target_data(&self) -> Option<TargetMessage> {
        self.last_target_data.lock().clone()
    }
}

pub struct ActionApp {
    config: Arc<Mutex<ActionConfig>>,
    action_processor: Option<ActionProcessor>,
    is_running: Option<Arc<AtomicBool>>,
    available_ports: Vec<String>,
    connection_status: String,
    last_target_info: Option<TargetMessage>,
    stats_aim_count: u32,
    stats_trigger_count: u32,
    network_discovery: Option<NetworkDiscovery>,
    show_network_panel: bool,
    selected_device: Option<String>,
}

impl ActionApp {    pub fn new(_cc: &eframe::CreationContext<'_>) -> Self {
        let config = Arc::new(Mutex::new(ActionConfig::load_from_file()));
        let available_ports = ArduinoController::get_available_ports().unwrap_or_default();
        
        let action_processor = ActionProcessor::new(config.clone()).ok();
        let is_running = action_processor.as_ref().map(|ap| ap.start_processing());
        
        // Initialize network discovery
        let mut network_discovery = NetworkDiscovery::new().ok();
        if let Some(ref mut discovery) = network_discovery {
            let _ = discovery.start_discovery();
        }
        
        // Start discovery responder
        let config_responder = config.clone();
        thread::spawn(move || {
            Self::start_discovery_responder(config_responder);
        });
        
        Self {
            config,
            action_processor,
            is_running,
            available_ports,
            connection_status: "Disconnected".to_string(),
            last_target_info: None,
            stats_aim_count: 0,
            stats_trigger_count: 0,
            network_discovery,
            show_network_panel: false,
            selected_device: None,
        }
    }
    
    fn start_discovery_responder(config: Arc<Mutex<ActionConfig>>) {
        if let Ok(socket) = UdpSocket::bind("0.0.0.0:5004") {
            socket.set_nonblocking(true).ok();
            
            loop {
                let mut buffer = [0u8; 1024];
                if let Ok((size, addr)) = socket.recv_from(&mut buffer) {
                    if let Ok(msg) = serde_json::from_slice::<serde_json::Value>(&buffer[..size]) {
                        if msg.get("device_type").and_then(|v| v.as_str()) == Some("scanner") {
                            let response = serde_json::json!({
                                "device_type": "action",
                                "device_name": "Action Control Device",
                                "version": "1.0",
                                "status": "active",
                                "port": config.lock().udp_listen_port
                            });
                            
                            if let Ok(response_data) = serde_json::to_vec(&response) {
                                let _ = socket.send_to(&response_data, addr);
                            }
                        }
                    }
                }
                thread::sleep(Duration::from_millis(10));
            }
        }
    }
}

impl eframe::App for ActionApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Update connection status and target info
        if let Some(ref processor) = self.action_processor {
            self.connection_status = if processor.is_arduino_connected() {
                "Connected".to_string()
            } else {
                "Disconnected".to_string()
            };
            
            if let Some(target_data) = processor.get_last_target_data() {
                self.last_target_info = Some(target_data);
                
                // Update statistics
                if self.last_target_info.as_ref().unwrap().message_type == "aim" {
                    self.stats_aim_count += 1;
                } else if self.last_target_info.as_ref().unwrap().message_type == "trigger" {
                    self.stats_trigger_count += 1;
                }
            }
        }

        // Handle keyboard shortcuts
        ctx.input(|i| {
            if i.key_pressed(egui::Key::F7) {
                let mut config = self.config.lock();
                config.trigger_bot = !config.trigger_bot;
            }
            
            if i.key_pressed(egui::Key::F8) {
                let mut config = self.config.lock();
                config.aim_assist = !config.aim_assist;
            }
        });

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("🎮 Action Control Engine");
            
            // Status indicators
            ui.horizontal(|ui| {
                let config = self.config.lock();
                
                if config.aim_assist {
                    ui.colored_label(egui::Color32::GREEN, "🎯 AIM: ACTIVE");
                } else {
                    ui.colored_label(egui::Color32::GRAY, "🎯 AIM: OFF");
                }
                
                if config.trigger_bot {
                    ui.colored_label(egui::Color32::RED, "🔫 TRIGGER: ARMED");
                } else {
                    ui.colored_label(egui::Color32::GRAY, "🔫 TRIGGER: SAFE");
                }
                
                if self.connection_status == "Connected" {
                    ui.colored_label(egui::Color32::GREEN, "🎮 ARDUINO: CONNECTED");
                } else {
                    ui.colored_label(egui::Color32::RED, "🎮 ARDUINO: DISCONNECTED");
                }
            });

            // Main controls
            ui.horizontal(|ui| {
                let mut config = self.config.lock();
                ui.checkbox(&mut config.aim_assist, "🎯 Aim Assist");
                ui.checkbox(&mut config.trigger_bot, "🔫 Trigger Bot");
                
                if ui.button("🔄 Reconnect Arduino").clicked() {
                    if let Some(ref processor) = self.action_processor {
                        let _ = processor.reconnect_arduino();
                    }
                }
                
                if ui.button("🛑 Stop All").clicked() {
                    if let Some(ref is_running) = self.is_running {
                        is_running.store(false, Ordering::Relaxed);
                    }
                    std::process::exit(0);
                }
                
                // Network discovery toggle
                if ui.button(if self.show_network_panel { "🌐 Hide Network" } else { "🌐 Show Network" }).clicked() {
                    self.show_network_panel = !self.show_network_panel;
                }
            });

            // Network status in status bar
            ui.horizontal(|ui| {
                if let Some(ref discovery) = self.network_discovery {
                    if discovery.is_scanning() {
                        ui.colored_label(egui::Color32::BLUE, "🌐 SCANNING");
                    } else {
                        ui.colored_label(egui::Color32::GRAY, "🌐 IDLE");
                    }
                    
                    if let Some(local_ip) = discovery.get_local_ip_string() {
                        ui.colored_label(egui::Color32::LIGHT_BLUE, format!("📡 Local IP: {}", local_ip));
                    }
                    
                    ui.label(format!("Devices: {}", discovery.get_devices().len()));
                }
            });

            // Network Discovery Panel
            if self.show_network_panel {
                ui.collapsing("🌐 Network Discovery", |ui| {
                    if let Some(ref mut discovery) = self.network_discovery {
                        ui.horizontal(|ui| {
                            if ui.button(if discovery.is_scanning() { "⏹️ Stop Scan" } else { "🔍 Start Scan" }).clicked() {
                                if discovery.is_scanning() {
                                    discovery.stop_discovery();
                                } else {
                                    let _ = discovery.start_discovery();
                                }
                            }
                            
                            ui.label("Looking for capture devices to receive data from");
                        });
                        
                        ui.separator();
                        
                        // Device list
                        egui::ScrollArea::vertical().max_height(200.0).show(ui, |ui| {
                            let devices = discovery.get_devices();
                            if devices.is_empty() {
                                ui.label("No capture devices found. Click 'Start Scan' to search for devices.");
                            } else {
                                for device in devices {
                                    // Only show capture devices for action app
                                    if let DeviceType::CaptureDevice = device.device_type {
                                        ui.horizontal(|ui| {
                                            let icon = "📸";
                                            
                                            // Status color
                                            let status_color = match device.status {
                                                ConnectionStatus::Connected => egui::Color32::GREEN,
                                                ConnectionStatus::Disconnected => egui::Color32::RED,
                                                ConnectionStatus::Scanning => egui::Color32::YELLOW,
                                                ConnectionStatus::Error(_) => egui::Color32::from_rgb(255, 165, 0),
                                            };
                                            
                                            ui.colored_label(status_color, icon);
                                            ui.label(&device.name);
                                            ui.label(format!("({})", device.ip));
                                            
                                            // Status indicator
                                            let status_text = match &device.status {
                                                ConnectionStatus::Connected => "✅ Connected",
                                                ConnectionStatus::Disconnected => "❌ Disconnected",
                                                ConnectionStatus::Scanning => "🔍 Scanning",
                                                ConnectionStatus::Error(msg) => &format!("⚠️ Error: {}", msg),
                                            };
                                            ui.colored_label(status_color, status_text);
                                            
                                            // Response time
                                            if let Some(response_time) = device.response_time {
                                                ui.label(format!("{}ms", response_time.as_millis()));
                                            }
                                            
                                            // Connect button for setting as data source
                                            if ui.button("📥 Set as Source").clicked() {
                                                self.selected_device = Some(device.ip.clone());
                                                let _ = discovery.connect_to_device(&device.ip);
                                            }
                                        });
                                        ui.separator();
                                    }
                                }
                            }
                        });
                        
                        // Current data source information
                        ui.separator();
                        ui.horizontal(|ui| {
                            ui.label("Listening on port:");
                            let listen_port = self.config.lock().udp_listen_port;
                            ui.colored_label(egui::Color32::LIGHT_BLUE, format!("{}", listen_port));
                            
                            if let Some(ref selected) = self.selected_device {
                                ui.label(format!("| Source: {}", selected));
                            }
                        });
                    } else {
                        ui.colored_label(egui::Color32::RED, "❌ Network discovery failed to initialize");
                        if ui.button("🔄 Retry").clicked() {
                            self.network_discovery = NetworkDiscovery::new().ok();
                            if let Some(ref mut discovery) = self.network_discovery {
                                let _ = discovery.start_discovery();
                            }
                        }
                    }
                });
            }
            
            ui.separator();
            
            // Configuration settings
            ui.collapsing("⚙️ Aim Settings", |ui| {
                let mut config = self.config.lock();
                
                ui.horizontal(|ui| {
                    ui.label("Aim Speed X:");
                    ui.add(egui::Slider::new(&mut config.aim_speed_x, 0.1..=2.0).step_by(0.1));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Aim Speed Y:");
                    ui.add(egui::Slider::new(&mut config.aim_speed_y, 0.1..=2.0).step_by(0.1));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Aim Offset Y:");
                    ui.add(egui::Slider::new(&mut config.aim_offset, -50..=50));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Aim Offset X:");
                    ui.add(egui::Slider::new(&mut config.aim_offset_x, -50..=50));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Aim FPS:");
                    ui.add(egui::Slider::new(&mut config.aim_fps, 60..=500));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Aim Key:");
                    ui.text_edit_singleline(&mut config.aim_key);
                    ui.label("(hex key code or 'auto')");
                });
            });
            
            ui.collapsing("🔫 Trigger Settings", |ui| {
                let mut config = self.config.lock();
                
                ui.horizontal(|ui| {
                    ui.label("Trigger Key (hex):");
                    let mut trigger_key_str = format!("0x{:02X}", config.trigger_key);
                    if ui.text_edit_singleline(&mut trigger_key_str).changed() {
                        if let Ok(key) = u32::from_str_radix(&trigger_key_str.trim_start_matches("0x"), 16) {
                            config.trigger_key = key;
                        }
                    }
                });
                
                ui.horizontal(|ui| {
                    ui.label("Trigger Delay:");
                    ui.add(egui::Slider::new(&mut config.trigger_delay, 0.0..=1.0).step_by(0.01).suffix("s"));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Trigger FPS:");
                    ui.add(egui::Slider::new(&mut config.trigger_fps, 60..=500));
                });
            });
            
            ui.collapsing("🎮 Arduino Settings", |ui| {
                let mut config = self.config.lock();
                
                ui.horizontal(|ui| {
                    ui.label("COM Port:");
                    
                    egui::ComboBox::from_label("")
                        .selected_text(&config.com_port)
                        .show_ui(ui, |ui| {
                            ui.selectable_value(&mut config.com_port, "auto".to_string(), "Auto Detect");
                            for port in &self.available_ports {
                                ui.selectable_value(&mut config.com_port, port.clone(), port);
                            }
                        });
                    
                    if ui.button("🔄 Refresh Ports").clicked() {
                        self.available_ports = ArduinoController::get_available_ports().unwrap_or_default();
                    }
                });
                
                ui.horizontal(|ui| {
                    ui.label("UDP Listen Port:");
                    ui.add(egui::DragValue::new(&mut config.udp_listen_port).range(1024..=65535));
                });
            });
            
            ui.horizontal(|ui| {
                let config = self.config.lock();
                if ui.button("💾 Save Config").clicked() {
                    let _ = config.save_to_file();
                }
                
                if ui.button("🔄 Reload Config").clicked() {
                    drop(config);
                    *self.config.lock() = ActionConfig::load_from_file();
                }
            });
            
            ui.separator();
            
            // Target information display
            ui.collapsing("🎯 Target Information", |ui| {
                if let Some(ref target_info) = self.last_target_info {
                    ui.label(format!("Type: {}", target_info.message_type));
                    ui.label(format!("Position: ({:.1}, {:.1})", target_info.center_x, target_info.center_y));
                    ui.label(format!("Confidence: {:.3}", target_info.confidence));
                    ui.label(format!("Capture Size: {}px", target_info.capture_size));
                } else {
                    ui.label("No target data received");
                }
                
                ui.separator();
                ui.label(format!("Aim Actions: {}", self.stats_aim_count));
                ui.label(format!("Trigger Actions: {}", self.stats_trigger_count));
            });
            
            ui.separator();
            ui.horizontal(|ui| {
                ui.colored_label(egui::Color32::YELLOW, "🎮 F7: Toggle Trigger | F8: Toggle Aim");
            });
        });

        ctx.request_repaint_after(Duration::from_millis(100));
    }
}

pub fn main() -> eframe::Result<()> {
    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([500.0, 700.0])
            .with_title("🎮 Action Control Engine")
            .with_resizable(true),
        vsync: false,
        ..Default::default()
    };
    
    eframe::run_native(
        "Action Control Engine",
        options,
        Box::new(|cc| Ok(Box::new(ActionApp::new(cc)))),
    )
}
