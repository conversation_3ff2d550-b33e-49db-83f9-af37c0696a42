use eframe::egui;
use parking_lot::Mutex;
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use std::thread;
use std::time::{Duration, Instant};
use std::net::UdpSocket;
use serde::{Deserialize, Serialize};
use serde_json;
use anyhow::{Result, Context};
use aes::Aes256;
use aes::cipher::{BlockDecrypt, KeyInit, generic_array::GenericArray};
use base64::{Engine as _, engine::general_purpose};
use serialport::{available_ports, SerialPort};
use winapi::um::winuser::{GetAsyncKeyState};
use windows::Win32::System::Threading::*;
use windows::Win32::Foundation::*;

mod network_discovery;
use network_discovery::{NetworkDiscovery, DeviceType, ConnectionStatus};

// Default encryption key (must match ai_capture.rs)
const DEFAULT_ENCRYPTION_KEY: &str = "SilenceCapture2024KeyForSecureUDP!!";

// AI Action modes (from main.py)
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
pub enum AIActionMode {
    AimAssist,
    SilentAim,
    TriggerBot,
    Hybrid, // Multiple modes active
}

impl Default for AIActionMode {
    fn default() -> Self {
        AIActionMode::AimAssist
    }
}

// Encryption utilities (same as ai_capture.rs)
struct CryptoUtils {
    cipher: Option<Aes256>,
}

impl CryptoUtils {
    fn new() -> Self {
        Self { cipher: None }
    }

    fn pad_data(data: &[u8]) -> Vec<u8> {
        let mut padded = data.to_vec();
        let padding_len = 16 - (data.len() % 16);
        for _ in 0..padding_len {
            padded.push(padding_len as u8);
        }
        padded
    }

    fn unpad_data(data: &[u8]) -> Result<Vec<u8>> {
        if data.is_empty() {
            return Err(anyhow::anyhow!("Empty data"));
        }
        
        let padding_len = data[data.len() - 1] as usize;
        if padding_len > 16 || padding_len > data.len() {
            return Err(anyhow::anyhow!("Invalid padding"));
        }
        
        Ok(data[..data.len() - padding_len].to_vec())
    }

    fn fast_decrypt(&mut self, encrypted_data: &str, key: &str) -> Result<String> {
        let data = general_purpose::STANDARD.decode(encrypted_data)
            .context("Failed to decode base64")?;
        
        if data.len() < 32 {
            return Err(anyhow::anyhow!("Encrypted data too short"));
        }

        if self.cipher.is_none() {
            let mut key_bytes = [0u8; 32];
            let key_data = key.as_bytes();
            let copy_len = key_data.len().min(32);
            key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
            self.cipher = Some(Aes256::new(GenericArray::from_slice(&key_bytes)));
        }

        let cipher = self.cipher.as_ref().unwrap();
        let encrypted_data = &data[16..];
        
        let mut decrypted = Vec::new();
        for chunk in encrypted_data.chunks(16) {
            let mut block = GenericArray::clone_from_slice(chunk);
            cipher.decrypt_block(&mut block);
            decrypted.extend_from_slice(&block);
        }
        
        let unpadded = Self::unpad_data(&decrypted)?;
        String::from_utf8(unpadded).context("Failed to convert decrypted data to string")
    }

    fn decrypt(encrypted_data: &str, key: &str) -> Result<String> {
        let data = general_purpose::STANDARD.decode(encrypted_data)
            .context("Failed to decode base64")?;
        
        if data.len() < 32 {
            return Err(anyhow::anyhow!("Encrypted data too short"));
        }

        let mut key_bytes = [0u8; 32];
        let key_data = key.as_bytes();
        let copy_len = key_data.len().min(32);
        key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
        
        let cipher = Aes256::new(GenericArray::from_slice(&key_bytes));
        let encrypted_data = &data[16..];
        
        let mut decrypted = Vec::new();
        for chunk in encrypted_data.chunks(16) {
            let mut block = GenericArray::clone_from_slice(chunk);
            cipher.decrypt_block(&mut block);
            decrypted.extend_from_slice(&block);
        }
        
        let unpadded = Self::unpad_data(&decrypted)?;
        String::from_utf8(unpadded).context("Failed to convert decrypted data to string")
    }
}

// UDP Message structure for receiving AI target data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AITargetMessage {
    pub center_x: f32,
    pub center_y: f32,
    pub confidence: f32,
    pub capture_size: u32,
    pub message_type: String, // "aim", "trigger", "silent", or "clear"
    pub timestamp: u64,
    pub detection_mode: String, // "color", "ai", or "hybrid"
    pub bbox: Option<[f32; 4]>, // For AI detections
    pub class_id: Option<i32>, // For AI detections
}

// Configuration for AI Action
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AIActionConfig {
    // Network settings
    pub udp_port: u16,
    pub enable_encryption: bool,
    pub encryption_key: String,
    
    // Arduino settings
    pub com_port: String,
    pub baud_rate: u32,
    
    // AI Action settings
    pub enable_ai_actions: bool,
    pub ai_action_mode: AIActionMode,
    
    // Aim assist settings (from main.py)
    pub aim_assist_enabled: bool,
    pub aim_assist_key: u8, // VK code
    pub aim_speed_x: f32,
    pub aim_speed_y: f32,
    pub aim_offset: i32,
    pub aim_offset_x: i32,
    pub aim_assist_distance_threshold: f32, // Distance threshold for activation
    pub aim_assist_cooldown_ms: u64,
    
    // Silent aim settings (from main.py)
    pub silent_aim_enabled: bool,
    pub silent_aim_key: u8, // VK code
    pub silent_aim_speed_multiplier: f32,
    pub silent_aim_cooldown_ms: u64,
    
    // Trigger bot settings
    pub trigger_enabled: bool,
    pub trigger_key: u8, // VK code
    pub trigger_delay: f32,
    pub trigger_confidence_threshold: f32,
    
    // Sensitivity and targeting
    pub sensitivity: f32,
    pub target_multipliers: [f32; 11], // Based on monitor scale from main.py
    pub activation_range: f32,
    
    // Performance settings
    pub action_interval_ms: u64,
}

impl Default for AIActionConfig {
    fn default() -> Self {
        Self {
            udp_port: 12345,
            enable_encryption: true,
            encryption_key: DEFAULT_ENCRYPTION_KEY.to_string(),
            com_port: "auto".to_string(),
            baud_rate: 115200,
            enable_ai_actions: false,
            ai_action_mode: AIActionMode::AimAssist,
            aim_assist_enabled: false,
            aim_assist_key: 0x01, // Left mouse button
            aim_speed_x: 1.0,
            aim_speed_y: 1.0,
            aim_offset: 0,
            aim_offset_x: 0,
            aim_assist_distance_threshold: 65.0,
            aim_assist_cooldown_ms: 12, // 0.0125 seconds from main.py
            silent_aim_enabled: false,
            silent_aim_key: 0x06, // X2 mouse button
            silent_aim_speed_multiplier: 1.0,
            silent_aim_cooldown_ms: 200,
            trigger_enabled: false,
            trigger_key: 0x02, // Right mouse button
            trigger_delay: 0.0,
            trigger_confidence_threshold: 0.5,
            sensitivity: 0.30, // From main.py SENS
            target_multipliers: [0.0, 1.01, 1.025, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05], // From main.py
            activation_range: 300.0,
            action_interval_ms: 1,
        }
    }
}

impl AIActionConfig {
    pub fn load_from_file() -> Self {
        match std::fs::read_to_string("ai_action_config.toml") {
            Ok(content) => {
                match toml::from_str::<AIActionConfig>(&content) {
                    Ok(config) => config,
                    Err(_) => Self::default()
                }
            }
            Err(_) => Self::default()
        }
    }

    pub fn save_to_file(&self) -> Result<()> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write("ai_action_config.toml", content)?;
        Ok(())
    }
    
    // Calculate aim speed based on sensitivity (from main.py)
    pub fn get_aim_speed(&self) -> f32 {
        1.0 * (1.0 / self.sensitivity)
    }
}

// Arduino communication (enhanced from existing action.rs)
pub struct ArduinoController {
    port: Box<dyn SerialPort>,
}

impl ArduinoController {
    pub fn new() -> Result<Self> {
        let port_name = Self::detect_arduino_port()?;
        let port = serialport::new(&port_name, 115200)
            .timeout(Duration::from_millis(100))
            .open()
            .context("Failed to open Arduino port")?;
        Ok(Self { port })
    }
    
    pub fn with_port(port_name: &str) -> Result<Self> {
        let port = serialport::new(port_name, 115200)
            .timeout(Duration::from_millis(100))
            .open()
            .context("Failed to open Arduino port")?;
        Ok(Self { port })
    }

    fn detect_arduino_port() -> Result<String> {
        let ports = available_ports().context("Failed to list serial ports")?;
        
        for port in ports {
            if let Some(info) = &port.port_type {
                match info {
                    serialport::SerialPortType::UsbPort(usb_info) => {
                        if usb_info.manufacturer.as_ref().map_or(false, |m| m.contains("Arduino")) ||
                           usb_info.product.as_ref().map_or(false, |p| p.contains("Arduino")) {
                            return Ok(port.port_name);
                        }
                    }
                    _ => {}
                }
            }
        }
        
        Err(anyhow::anyhow!("No Arduino found"))
    }
    
    pub fn get_available_ports() -> Result<Vec<String>> {
        let ports = available_ports().context("Failed to list serial ports")?;
        Ok(ports.into_iter().map(|p| p.port_name).collect())
    }

    // Enhanced mouse movement for aim assist (from main.py)
    pub fn mousemove_aim(&mut self, x: i32, y: i32, message: &str) -> Result<()> {
        let mut x_coord = if x < 0 { x + 256 } else { x };
        let mut y_coord = if y < 0 { y + 256 } else { y };

        x_coord = x_coord.max(0).min(255);
        y_coord = y_coord.max(0).min(255);

        let coord_bytes = [x_coord as u8, y_coord as u8];
        let message_bytes = format!("{}\n", message).into_bytes();

        self.port.write_all(&coord_bytes)
            .context("Failed to write coordinates to Arduino")?;
        self.port.write_all(&message_bytes)
            .context("Failed to write message to Arduino")?;

        Ok(())
    }

    // Silent aim implementation (from main.py)
    pub fn silent_aim(&mut self, x_diff: i32, y_diff: i32) -> Result<()> {
        let data = format!("silent{}:{}", x_diff, y_diff);
        self.port.write_all(data.as_bytes())
            .context("Failed to write silent aim data to Arduino")?;
        Ok(())
    }

    // Trigger bot click
    pub fn mouse_click(&mut self) -> Result<()> {
        self.mousemove_aim(0, 0, "mouseclick")
    }

    // Shoot command (from main.py)
    pub fn shoot(&mut self) -> Result<()> {
        self.port.write_all("shoot".encode())
            .context("Failed to write shoot command to Arduino")?;
        Ok(())
    }
}

// AI Action Processor with enhanced features from main.py
pub struct AIActionProcessor {
    config: Arc<Mutex<AIActionConfig>>,
    arduino: Arc<Mutex<Option<ArduinoController>>>,
    last_aim_time: Instant,
    last_silent_time: Instant,
    last_trigger_time: Instant,
    udp_socket: UdpSocket,
    last_target_data: Arc<Mutex<Option<AITargetMessage>>>,
    crypto_utils: CryptoUtils,

    // Cooldown tracking
    aim_assist_cooldown: Arc<Mutex<bool>>,
    silent_aim_cooldown: Arc<Mutex<bool>>,
    trigger_cooldown: Arc<Mutex<bool>>,

    // Statistics
    stats: Arc<Mutex<AIActionStats>>,
}

#[derive(Debug, Default)]
pub struct AIActionStats {
    pub total_actions: u32,
    pub aim_assist_actions: u32,
    pub silent_aim_actions: u32,
    pub trigger_actions: u32,
    pub last_action_time: Option<Instant>,
    pub avg_target_distance: f32,
    pub successful_hits: u32,
}

impl AIActionProcessor {
    pub fn new(config: Arc<Mutex<AIActionConfig>>) -> Result<Self> {
        let udp_port = config.lock().udp_port;
        let socket = UdpSocket::bind(format!("0.0.0.0:{}", udp_port))?;
        socket.set_nonblocking(true)?;

        let arduino = if config.lock().com_port == "auto" {
            match ArduinoController::new() {
                Ok(controller) => Some(controller),
                Err(_) => None,
            }
        } else {
            match ArduinoController::with_port(&config.lock().com_port) {
                Ok(controller) => Some(controller),
                Err(_) => None,
            }
        };

        Ok(Self {
            config,
            arduino: Arc::new(Mutex::new(arduino)),
            last_aim_time: Instant::now(),
            last_silent_time: Instant::now(),
            last_trigger_time: Instant::now(),
            udp_socket: socket,
            last_target_data: Arc::new(Mutex::new(None)),
            crypto_utils: CryptoUtils::new(),
            aim_assist_cooldown: Arc::new(Mutex::new(true)),
            silent_aim_cooldown: Arc::new(Mutex::new(true)),
            trigger_cooldown: Arc::new(Mutex::new(true)),
            stats: Arc::new(Mutex::new(AIActionStats::default())),
        })
    }

    pub fn reconnect_arduino(&self) -> Result<()> {
        let config = self.config.lock();
        let new_arduino = if config.com_port == "auto" {
            ArduinoController::new()?
        } else {
            ArduinoController::with_port(&config.com_port)?
        };
        *self.arduino.lock() = Some(new_arduino);
        Ok(())
    }

    pub fn is_arduino_connected(&self) -> bool {
        self.arduino.lock().is_some()
    }

    pub fn start_processing(&self) -> Arc<AtomicBool> {
        let is_running = Arc::new(AtomicBool::new(true));

        // Start UDP listener thread
        let udp_socket = self.udp_socket.try_clone().unwrap();
        let target_data = self.last_target_data.clone();
        let config_clone = self.config.clone();
        let running_clone = is_running.clone();
        let mut crypto_utils = CryptoUtils::new();

        thread::spawn(move || {
            let mut buffer = [0u8; 1024];

            while running_clone.load(Ordering::Relaxed) {
                match udp_socket.recv_from(&mut buffer) {
                    Ok((size, _addr)) => {
                        let data = &buffer[..size];
                        let config = config_clone.lock();

                        let message_str = if config.enable_encryption {
                            let encrypted_str = String::from_utf8_lossy(data);
                            match crypto_utils.fast_decrypt(&encrypted_str, &config.encryption_key) {
                                Ok(decrypted) => decrypted,
                                Err(_) => continue,
                            }
                        } else {
                            String::from_utf8_lossy(data).to_string()
                        };

                        if let Ok(message) = serde_json::from_str::<AITargetMessage>(&message_str) {
                            *target_data.lock() = Some(message);
                        }
                    }
                    Err(_) => {
                        thread::sleep(Duration::from_millis(1));
                    }
                }
            }
        });

        // Start action processing thread
        let config_clone = self.config.clone();
        let arduino_clone = self.arduino.clone();
        let target_data_clone = self.last_target_data.clone();
        let running_clone = is_running.clone();
        let aim_cooldown = self.aim_assist_cooldown.clone();
        let silent_cooldown = self.silent_aim_cooldown.clone();
        let trigger_cooldown = self.trigger_cooldown.clone();
        let stats_clone = self.stats.clone();

        thread::spawn(move || {
            while running_clone.load(Ordering::Relaxed) {
                let config = config_clone.lock();

                if config.enable_ai_actions {
                    // Process different AI action modes
                    match config.ai_action_mode {
                        AIActionMode::AimAssist => {
                            Self::process_aim_assist_static(&config, &arduino_clone, &target_data_clone, &aim_cooldown, &stats_clone);
                        }
                        AIActionMode::SilentAim => {
                            Self::process_silent_aim_static(&config, &arduino_clone, &target_data_clone, &silent_cooldown, &stats_clone);
                        }
                        AIActionMode::TriggerBot => {
                            Self::process_trigger_bot_static(&config, &arduino_clone, &target_data_clone, &trigger_cooldown, &stats_clone);
                        }
                        AIActionMode::Hybrid => {
                            // Process all modes
                            Self::process_aim_assist_static(&config, &arduino_clone, &target_data_clone, &aim_cooldown, &stats_clone);
                            Self::process_silent_aim_static(&config, &arduino_clone, &target_data_clone, &silent_cooldown, &stats_clone);
                            Self::process_trigger_bot_static(&config, &arduino_clone, &target_data_clone, &trigger_cooldown, &stats_clone);
                        }
                    }
                }

                drop(config);
                thread::sleep(Duration::from_millis(1));
            }
        });

        is_running
    }

    // Aim assist processing (based on main.py logic)
    fn process_aim_assist_static(
        config: &AIActionConfig,
        arduino: &Arc<Mutex<Option<ArduinoController>>>,
        target_data: &Arc<Mutex<Option<AITargetMessage>>>,
        cooldown: &Arc<Mutex<bool>>,
        stats: &Arc<Mutex<AIActionStats>>,
    ) {
        if !config.aim_assist_enabled || !*cooldown.lock() {
            return;
        }

        let aim_pressed = unsafe {
            GetAsyncKeyState(config.aim_assist_key as i32) < 0
        };

        if !aim_pressed {
            return;
        }

        if let Some(target_msg) = target_data.lock().clone() {
            if target_msg.message_type == "aim" || target_msg.message_type == "clear" {
                if target_msg.message_type == "clear" {
                    return;
                }

                let fov_center = target_msg.capture_size as f32 / 2.0;
                let x_offset = target_msg.center_x - fov_center;
                let y_offset = target_msg.center_y - fov_center;

                // Calculate distance for threshold check
                let distance = (x_offset.powi(2) + y_offset.powi(2)).sqrt();

                if distance > config.aim_assist_distance_threshold {
                    return;
                }

                let adjusted_y = y_offset - config.aim_offset as f32;
                let adjusted_x = x_offset - config.aim_offset_x as f32;

                let aim_speed = config.get_aim_speed();
                let monitor_scale = 5; // Default from main.py
                let target_multiplier = if monitor_scale < config.target_multipliers.len() {
                    config.target_multipliers[monitor_scale]
                } else {
                    1.0
                };

                let mut x_move = (adjusted_x * config.aim_speed_x * aim_speed * target_multiplier) as i32;
                let mut y_move = (adjusted_y * config.aim_speed_y * aim_speed * target_multiplier) as i32;

                // Apply smoothing based on distance (from main.py)
                if x_move.abs() > 20 {
                    x_move = (x_move as f32 * 0.4) as i32;
                } else {
                    x_move = (x_move as f32 * 0.4) as i32;
                }

                if y_move.abs() > 20 {
                    y_move = (y_move as f32 * 0.2) as i32;
                } else {
                    y_move = (y_move as f32 * 0.2) as i32;
                }

                if let Some(ref mut arduino_controller) = arduino.lock().as_mut() {
                    let _ = arduino_controller.mousemove_aim(x_move, y_move, "movemouse");

                    // Update statistics
                    let mut stats_guard = stats.lock();
                    stats_guard.total_actions += 1;
                    stats_guard.aim_assist_actions += 1;
                    stats_guard.last_action_time = Some(Instant::now());
                    stats_guard.avg_target_distance = (stats_guard.avg_target_distance + distance) / 2.0;
                }

                // Set cooldown
                *cooldown.lock() = false;
                let cooldown_clone = cooldown.clone();
                let cooldown_ms = config.aim_assist_cooldown_ms;
                thread::spawn(move || {
                    thread::sleep(Duration::from_millis(cooldown_ms));
                    *cooldown_clone.lock() = true;
                });
            }
        }
    }

    // Silent aim processing (based on main.py logic)
    fn process_silent_aim_static(
        config: &AIActionConfig,
        arduino: &Arc<Mutex<Option<ArduinoController>>>,
        target_data: &Arc<Mutex<Option<AITargetMessage>>>,
        cooldown: &Arc<Mutex<bool>>,
        stats: &Arc<Mutex<AIActionStats>>,
    ) {
        if !config.silent_aim_enabled || !*cooldown.lock() {
            return;
        }

        let silent_pressed = unsafe {
            GetAsyncKeyState(config.silent_aim_key as i32) < 0
        };

        if !silent_pressed {
            return;
        }

        if let Some(target_msg) = target_data.lock().clone() {
            if target_msg.message_type == "aim" || target_msg.message_type == "silent" {
                if target_msg.message_type == "clear" {
                    return;
                }

                let fov_center = target_msg.capture_size as f32 / 2.0;
                let x_offset = target_msg.center_x - fov_center;
                let y_offset = target_msg.center_y - fov_center;

                let adjusted_y = y_offset - config.aim_offset as f32;
                let adjusted_x = x_offset - config.aim_offset_x as f32;

                let aim_speed = config.get_aim_speed();
                let monitor_scale = 5; // Default from main.py
                let target_multiplier = if monitor_scale < config.target_multipliers.len() {
                    config.target_multipliers[monitor_scale]
                } else {
                    1.0
                };

                let x_move = (adjusted_x * aim_speed * target_multiplier * config.silent_aim_speed_multiplier) as i32;
                let y_move = (adjusted_y * aim_speed * target_multiplier * config.silent_aim_speed_multiplier) as i32;

                if let Some(ref mut arduino_controller) = arduino.lock().as_mut() {
                    let _ = arduino_controller.silent_aim(x_move, y_move);

                    // Update statistics
                    let mut stats_guard = stats.lock();
                    stats_guard.total_actions += 1;
                    stats_guard.silent_aim_actions += 1;
                    stats_guard.last_action_time = Some(Instant::now());
                }

                // Set cooldown
                *cooldown.lock() = false;
                let cooldown_clone = cooldown.clone();
                let cooldown_ms = config.silent_aim_cooldown_ms;
                thread::spawn(move || {
                    thread::sleep(Duration::from_millis(cooldown_ms));
                    *cooldown_clone.lock() = true;
                });
            }
        }
    }

    // Trigger bot processing
    fn process_trigger_bot_static(
        config: &AIActionConfig,
        arduino: &Arc<Mutex<Option<ArduinoController>>>,
        target_data: &Arc<Mutex<Option<AITargetMessage>>>,
        cooldown: &Arc<Mutex<bool>>,
        stats: &Arc<Mutex<AIActionStats>>,
    ) {
        if !config.trigger_enabled || !*cooldown.lock() {
            return;
        }

        let trigger_pressed = unsafe {
            GetAsyncKeyState(config.trigger_key as i32) < 0
        };

        if !trigger_pressed {
            return;
        }

        if let Some(target_msg) = target_data.lock().clone() {
            if target_msg.message_type == "trigger" && target_msg.confidence >= config.trigger_confidence_threshold {
                thread::sleep(Duration::from_secs_f32(config.trigger_delay));

                if let Some(ref mut arduino_controller) = arduino.lock().as_mut() {
                    let _ = arduino_controller.shoot();

                    // Update statistics
                    let mut stats_guard = stats.lock();
                    stats_guard.total_actions += 1;
                    stats_guard.trigger_actions += 1;
                    stats_guard.last_action_time = Some(Instant::now());
                    stats_guard.successful_hits += 1;
                }

                // Set cooldown
                *cooldown.lock() = false;
                let cooldown_clone = cooldown.clone();
                thread::spawn(move || {
                    thread::sleep(Duration::from_millis(100)); // Short cooldown for trigger
                    *cooldown_clone.lock() = true;
                });
            }
        }
    }

    pub fn get_last_target_data(&self) -> Option<AITargetMessage> {
        self.last_target_data.lock().clone()
    }

    pub fn get_stats(&self) -> AIActionStats {
        self.stats.lock().clone()
    }
}

// Main AI Action Application
pub struct AIActionApp {
    config: Arc<Mutex<AIActionConfig>>,
    action_processor: Option<AIActionProcessor>,
    is_running: Option<Arc<AtomicBool>>,
    available_ports: Vec<String>,
    connection_status: String,
    last_target_info: Option<AITargetMessage>,
    stats: AIActionStats,
    network_discovery: Option<NetworkDiscovery>,
    show_network_panel: bool,
    selected_device: Option<String>,

    // UI state
    show_advanced_settings: bool,
    show_statistics: bool,
}

impl Default for AIActionApp {
    fn default() -> Self {
        Self::new()
    }
}

impl AIActionApp {
    pub fn new() -> Self {
        let config = Arc::new(Mutex::new(AIActionConfig::load_from_file()));
        let available_ports = ArduinoController::get_available_ports().unwrap_or_default();

        Self {
            config,
            action_processor: None,
            is_running: None,
            available_ports,
            connection_status: "Disconnected".to_string(),
            last_target_info: None,
            stats: AIActionStats::default(),
            network_discovery: None,
            show_network_panel: false,
            selected_device: None,
            show_advanced_settings: false,
            show_statistics: true,
        }
    }

    pub fn start_processing(&mut self) {
        if self.action_processor.is_none() {
            match AIActionProcessor::new(self.config.clone()) {
                Ok(processor) => {
                    let is_running = processor.start_processing();
                    self.connection_status = if processor.is_arduino_connected() {
                        "Connected".to_string()
                    } else {
                        "Arduino not found".to_string()
                    };
                    self.action_processor = Some(processor);
                    self.is_running = Some(is_running);
                }
                Err(e) => {
                    self.connection_status = format!("Error: {}", e);
                }
            }
        }
    }

    pub fn stop_processing(&mut self) {
        if let Some(is_running) = &self.is_running {
            is_running.store(false, Ordering::Relaxed);
        }
        self.action_processor = None;
        self.is_running = None;
        self.connection_status = "Stopped".to_string();
    }

    pub fn reconnect_arduino(&mut self) {
        if let Some(ref processor) = self.action_processor {
            match processor.reconnect_arduino() {
                Ok(_) => self.connection_status = "Reconnected".to_string(),
                Err(e) => self.connection_status = format!("Reconnect failed: {}", e),
            }
        }
    }

    fn start_network_discovery(&mut self) {
        if self.network_discovery.is_none() {
            match NetworkDiscovery::new() {
                Ok(mut discovery) => {
                    if discovery.start_discovery().is_ok() {
                        self.network_discovery = Some(discovery);
                    }
                }
                Err(_) => {}
            }
        }
    }
}

impl eframe::App for AIActionApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Update data from processor
        if let Some(ref processor) = self.action_processor {
            self.last_target_info = processor.get_last_target_data();
            self.stats = processor.get_stats();
        }

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("🎯 AI Action Controller");

            // Status and control buttons
            ui.horizontal(|ui| {
                ui.label(format!("Status: {}", self.connection_status));
                ui.separator();

                if ui.button(if self.is_running.is_some() { "⏹ Stop" } else { "▶ Start" }).clicked() {
                    if self.is_running.is_some() {
                        self.stop_processing();
                    } else {
                        self.start_processing();
                    }
                }

                if ui.button("🔄 Reconnect Arduino").clicked() {
                    self.reconnect_arduino();
                }

                if ui.button("🌐 Network").clicked() {
                    self.show_network_panel = !self.show_network_panel;
                    if self.show_network_panel {
                        self.start_network_discovery();
                    }
                }
            });

            ui.separator();

            // AI Action Mode Selection
            ui.horizontal(|ui| {
                ui.label("AI Action Mode:");
                let mut config = self.config.lock();
                egui::ComboBox::from_label("")
                    .selected_text(format!("{:?}", config.ai_action_mode))
                    .show_ui(ui, |ui| {
                        ui.selectable_value(&mut config.ai_action_mode, AIActionMode::AimAssist, "Aim Assist");
                        ui.selectable_value(&mut config.ai_action_mode, AIActionMode::SilentAim, "Silent Aim");
                        ui.selectable_value(&mut config.ai_action_mode, AIActionMode::TriggerBot, "Trigger Bot");
                        ui.selectable_value(&mut config.ai_action_mode, AIActionMode::Hybrid, "Hybrid (All)");
                    });

                ui.separator();
                ui.checkbox(&mut config.enable_ai_actions, "🤖 Enable AI Actions");
            });

            ui.separator();

            // Quick toggles for individual features
            ui.horizontal(|ui| {
                let mut config = self.config.lock();
                ui.checkbox(&mut config.aim_assist_enabled, "🎯 Aim Assist");
                ui.checkbox(&mut config.silent_aim_enabled, "👻 Silent Aim");
                ui.checkbox(&mut config.trigger_enabled, "🔫 Trigger Bot");
            });

            ui.separator();

            // Aim Assist Configuration
            ui.collapsing("🎯 Aim Assist Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("Aim Speed X:");
                    ui.add(egui::Slider::new(&mut config.aim_speed_x, 0.1..=5.0).step_by(0.1));
                });

                ui.horizontal(|ui| {
                    ui.label("Aim Speed Y:");
                    ui.add(egui::Slider::new(&mut config.aim_speed_y, 0.1..=5.0).step_by(0.1));
                });

                ui.horizontal(|ui| {
                    ui.label("Aim Offset:");
                    ui.add(egui::DragValue::new(&mut config.aim_offset).range(-50..=50));
                });

                ui.horizontal(|ui| {
                    ui.label("Aim Offset X:");
                    ui.add(egui::DragValue::new(&mut config.aim_offset_x).range(-50..=50));
                });

                ui.horizontal(|ui| {
                    ui.label("Distance Threshold:");
                    ui.add(egui::Slider::new(&mut config.aim_assist_distance_threshold, 10.0..=200.0));
                });

                ui.horizontal(|ui| {
                    ui.label("Cooldown (ms):");
                    ui.add(egui::DragValue::new(&mut config.aim_assist_cooldown_ms).range(1..=100));
                });

                ui.horizontal(|ui| {
                    ui.label("Activation Key:");
                    ui.label(format!("VK_{:02X}", config.aim_assist_key));
                    if ui.button("Change").clicked() {
                        // Key binding UI could be implemented here
                    }
                });
            });

            // Silent Aim Configuration
            ui.collapsing("👻 Silent Aim Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("Speed Multiplier:");
                    ui.add(egui::Slider::new(&mut config.silent_aim_speed_multiplier, 0.1..=3.0).step_by(0.1));
                });

                ui.horizontal(|ui| {
                    ui.label("Cooldown (ms):");
                    ui.add(egui::DragValue::new(&mut config.silent_aim_cooldown_ms).range(50..=1000));
                });

                ui.horizontal(|ui| {
                    ui.label("Activation Key:");
                    ui.label(format!("VK_{:02X}", config.silent_aim_key));
                    if ui.button("Change").clicked() {
                        // Key binding UI could be implemented here
                    }
                });
            });

            // Trigger Bot Configuration
            ui.collapsing("🔫 Trigger Bot Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("Trigger Delay (s):");
                    ui.add(egui::Slider::new(&mut config.trigger_delay, 0.0..=1.0).step_by(0.01));
                });

                ui.horizontal(|ui| {
                    ui.label("Confidence Threshold:");
                    ui.add(egui::Slider::new(&mut config.trigger_confidence_threshold, 0.1..=1.0).step_by(0.05));
                });

                ui.horizontal(|ui| {
                    ui.label("Activation Key:");
                    ui.label(format!("VK_{:02X}", config.trigger_key));
                    if ui.button("Change").clicked() {
                        // Key binding UI could be implemented here
                    }
                });
            });

            // Advanced Settings
            if self.show_advanced_settings {
                ui.collapsing("⚙️ Advanced Settings", |ui| {
                    let mut config = self.config.lock();

                    ui.horizontal(|ui| {
                        ui.label("Sensitivity:");
                        ui.add(egui::Slider::new(&mut config.sensitivity, 0.1..=2.0).step_by(0.01));
                    });

                    ui.horizontal(|ui| {
                        ui.label("Activation Range:");
                        ui.add(egui::Slider::new(&mut config.activation_range, 50.0..=500.0));
                    });

                    ui.horizontal(|ui| {
                        ui.label("Action Interval (ms):");
                        ui.add(egui::DragValue::new(&mut config.action_interval_ms).range(1..=50));
                    });

                    ui.separator();

                    ui.label("Target Multipliers (by monitor scale):");
                    for (i, multiplier) in config.target_multipliers.iter_mut().enumerate() {
                        ui.horizontal(|ui| {
                            ui.label(format!("Scale {}:", i));
                            ui.add(egui::Slider::new(multiplier, 0.5..=2.0).step_by(0.01));
                        });
                    }
                });
            }

            // Arduino Configuration
            ui.collapsing("🔌 Arduino Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("COM Port:");
                    egui::ComboBox::from_label("")
                        .selected_text(&config.com_port)
                        .show_ui(ui, |ui| {
                            ui.selectable_value(&mut config.com_port, "auto".to_string(), "Auto Detect");
                            for port in &self.available_ports {
                                ui.selectable_value(&mut config.com_port, port.clone(), port);
                            }
                        });
                });

                ui.horizontal(|ui| {
                    ui.label("Baud Rate:");
                    ui.add(egui::DragValue::new(&mut config.baud_rate).range(9600..=115200));
                });

                if ui.button("🔄 Refresh Ports").clicked() {
                    self.available_ports = ArduinoController::get_available_ports().unwrap_or_default();
                }
            });

            // Network Configuration
            ui.collapsing("🌐 Network Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("UDP Port:");
                    ui.add(egui::DragValue::new(&mut config.udp_port).range(1024..=65535));
                });

                ui.checkbox(&mut config.enable_encryption, "🔒 Enable UDP Encryption");

                if self.show_network_panel {
                    if let Some(ref mut discovery) = self.network_discovery {
                        let devices = discovery.get_discovered_devices();

                        ui.separator();
                        ui.label(format!("Discovered {} capture devices:", devices.len()));

                        for (ip, device) in devices {
                            if device.device_type == DeviceType::CaptureDevice {
                                ui.horizontal(|ui| {
                                    ui.label(&ip);
                                    ui.label(&device.device_name);

                                    if ui.button("Connect").clicked() {
                                        self.selected_device = Some(ip.clone());
                                        // Auto-configure to connect to this capture device
                                    }
                                });
                            }
                        }
                    }
                }
            });

            // Statistics Display
            if self.show_statistics {
                ui.collapsing("📊 Action Statistics", |ui| {
                    ui.horizontal(|ui| {
                        ui.label(format!("Total Actions: {}", self.stats.total_actions));
                        ui.separator();
                        ui.label(format!("Aim Assist: {}", self.stats.aim_assist_actions));
                        ui.separator();
                        ui.label(format!("Silent Aim: {}", self.stats.silent_aim_actions));
                        ui.separator();
                        ui.label(format!("Trigger: {}", self.stats.trigger_actions));
                    });

                    ui.horizontal(|ui| {
                        ui.label(format!("Successful Hits: {}", self.stats.successful_hits));
                        ui.separator();
                        ui.label(format!("Avg Target Distance: {:.1}", self.stats.avg_target_distance));
                    });

                    if let Some(last_time) = self.stats.last_action_time {
                        ui.label(format!("Last Action: {:.1}s ago", last_time.elapsed().as_secs_f32()));
                    }

                    if ui.button("🔄 Reset Statistics").clicked() {
                        self.stats = AIActionStats::default();
                    }
                });
            }

            // Target Information Display
            if let Some(ref target) = self.last_target_info {
                ui.collapsing("🎯 Current Target Info", |ui| {
                    ui.horizontal(|ui| {
                        ui.label(format!("Position: ({:.1}, {:.1})", target.center_x, target.center_y));
                        ui.separator();
                        ui.label(format!("Confidence: {:.2}", target.confidence));
                        ui.separator();
                        ui.label(format!("Type: {}", target.message_type));
                    });

                    ui.horizontal(|ui| {
                        ui.label(format!("Detection: {}", target.detection_mode));
                        ui.separator();
                        ui.label(format!("Capture Size: {}", target.capture_size));
                    });

                    if let Some(bbox) = target.bbox {
                        ui.label(format!("Bounding Box: [{:.1}, {:.1}, {:.1}, {:.1}]", bbox[0], bbox[1], bbox[2], bbox[3]));
                    }

                    if let Some(class_id) = target.class_id {
                        ui.label(format!("Class ID: {}", class_id));
                    }
                });
            }

            ui.separator();

            // Control buttons
            ui.horizontal(|ui| {
                ui.checkbox(&mut self.show_advanced_settings, "⚙️ Advanced");
                ui.checkbox(&mut self.show_statistics, "📊 Statistics");

                if ui.button("💾 Save Config").clicked() {
                    let _ = self.config.lock().save_to_file();
                }

                if ui.button("🔄 Load Config").clicked() {
                    *self.config.lock() = AIActionConfig::load_from_file();
                }
            });
        });

        ctx.request_repaint();
    }
}

// Main function for running AI action as standalone binary
fn main() -> eframe::Result<()> {
    // Set high priority for better performance
    unsafe {
        use windows::Win32::System::Threading::*;
        let process = GetCurrentProcess();
        let _ = SetPriorityClass(process, HIGH_PRIORITY_CLASS);
    }

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([700.0, 900.0])
            .with_title("🎯 AI Action Controller")
            .with_resizable(true),
        vsync: false,
        ..Default::default()
    };

    eframe::run_native(
        "AI Action Controller",
        options,
        Box::new(|_cc| Ok(Box::new(AIActionApp::new()))),
    )
}
