import bettercam
import torch
import cv2
import numpy as np
import time
import math
import keyboard
import threading
import serial
import tkinter as tk
import pywintypes
import win32api
import win32con
from colorama import Fore
from ultralytics import YOLO
import pandas as pd
import os
import mouse


def cooldown(cooldown_bool,wait):
    time.sleep(wait)
    cooldown_bool[0] = True


def labels():
    #This function contains all the labels used and is threaded so t<PERSON>nter can run a ui 
    global fps_label
    global trigger_label
    global assist_label
    global color_assist_label
    global silent_label
    global fov_label
    fps_label = tk.Label(text = "  ", font=('Tahoma','10'), fg='white', bg='black')
    fps_label.master.overrideredirect(True)
    fps_label.master.geometry("+14+16")
    fps_label.master.lift()
    fps_label.master.wm_attributes("-topmost", True)
    fps_label.master.wm_attributes("-disabled", True)
    fps_label.master.wm_attributes("-transparentcolor", "black")
    fps_label.pack()
    fov_label = tk.Label(text = f"FOV: {activation_range}", font=('Tahoma','10'), fg='white', bg='black')
    fov_label.master.overrideredirect(True)
    fov_label.master.lift()
    fov_label.master.wm_attributes("-topmost", True)
    fov_label.master.wm_attributes("-disabled", True)
    fov_label.master.wm_attributes("-transparentcolor", "black")
    fov_label.pack()

    trigger_label = tk.Label(text = "Triggerbot: Unactive", font=('Tahoma','10'), fg='red', bg='black')
    trigger_label.master.overrideredirect(True)
    trigger_label.master.lift()
    trigger_label.master.wm_attributes("-topmost", True)
    trigger_label.master.wm_attributes("-disabled", True)
    trigger_label.master.wm_attributes("-transparentcolor", "black")
    
    trigger_label.pack()

    assist_label = tk.Label(text = "Aim Assist: Unactive", font=('Tahoma','10'), fg='red', bg='black')
    assist_label.master.overrideredirect(True)
    assist_label.master.lift()
    assist_label.master.wm_attributes("-topmost", True)
    assist_label.master.wm_attributes("-disabled", True)
    assist_label.master.wm_attributes("-transparentcolor", "black")
    assist_label.pack()

    color_assist_label = tk.Label(text = "Color Aim Assist: Unactive", font=('Tahoma','10'), fg='red', bg='black')
    color_assist_label.master.overrideredirect(True)
    color_assist_label.master.lift()
    color_assist_label.master.wm_attributes("-topmost", True)
    color_assist_label.master.wm_attributes("-disabled", True)
    color_assist_label.master.wm_attributes("-transparentcolor", "black")
    color_assist_label.pack()

    silent_label = tk.Label(text = "Silent aim: Unactive", font=('Tahoma','10'), fg='red', bg='black')
    silent_label.master.overrideredirect(True)
    silent_label.master.lift()
    silent_label.master.wm_attributes("-topmost", True)
    silent_label.master.wm_attributes("-disabled", True)
    silent_label.master.wm_attributes("-transparentcolor", "black")
    silent_label.pack()


    hWindow = pywintypes.HANDLE(int(fps_label.master.frame(), 16))
    exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
    win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
    hWindow = pywintypes.HANDLE(int(assist_label.master.frame(), 16))
    exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
    win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
    hWindow = pywintypes.HANDLE(int(color_assist_label.master.frame(), 16))
    exStyle = win32con.WS_EX_COMPOSITED | win32con.WS_EX_LAYERED | win32con.WS_EX_NOACTIVATE | win32con.WS_EX_TOPMOST | win32con.WS_EX_TRANSPARENT
    win32api.SetWindowLong(hWindow, win32con.GWL_EXSTYLE, exStyle)
    fps_label.mainloop()



SENS = 0.30
AIM_SPEED = 1*(1/SENS)
target_multiply = [0,1.01,1.025,1.05,1.05,1.05,1.05,1.05,1.05,1.05,1.05]
serialcomm = serial.Serial("COM12",115200,timeout = 0)
activation_range = 300

ui = threading.Thread(target=labels, args=())
ui.start()




MONITOR_WIDTH = 1920#game res
MONITOR_HEIGHT = 1080#game res
MONITOR_SCALE = 5 #how much the screen shot is downsized by eg. 5 would be one fifth of the monitor dimensions
region = (int(MONITOR_WIDTH/2-MONITOR_WIDTH/MONITOR_SCALE/2),int(MONITOR_HEIGHT/2-MONITOR_HEIGHT/MONITOR_SCALE/2),int(MONITOR_WIDTH/2+MONITOR_WIDTH/MONITOR_SCALE/2),int(MONITOR_HEIGHT/2+MONITOR_HEIGHT/MONITOR_SCALE/2))
x,y,width,height = region
screenshot_center = [int((width-x)/2),int((height-y)/2)]
triggerbot = False
triggerbot_toggle = [True]

aim_assist = False
aim_assist_toggle = [True]
send_next = [True]


silent_aim = False
silent_aim_not_cooldown = [True]
silent_toggle = [True]


no_fov_cooldown = [True]
torch.cuda.set_device(0)
#model = torch.hub.load(path= r'A:\TEMP\Valorant-AI-cheats-main\scripts\best_custom.engine',source='local').cuda()
model = YOLO(r"G:\silence-ai\custom_best.engine", task='detect')
camera = bettercam.create(output_idx=0, output_color="RGB")

start_time = time.time()
x = 1
counter = 0






while True:
    closest_part_distance = 100000
    closest_part = -1
    screenshot = camera.grab(region)
    if screenshot is None: continue
    results = model.predict(screenshot, imgsz=736, classes=1, conf=0.4, max_det=300, device="cuda:0" , verbose=False)
    data = []
    for box in results[0].boxes.xyxy:
        # Extract bounding box coordinates
        xmin, ymin, xmax, ymax = box[0], box[1], box[2], box[3]

        # Initialize confidence score and class label to None
        confidence = None
        class_label = None

        # Check if box has sufficient elements
        if len(box) >= 5:
            confidence = box[4]  # Extract confidence score
        if len(box) >= 6:
            class_label = box[5]  # Extract class label

        # Create a dictionary containing the extracted information
        box_data = {
            'xmin': xmin,
            'ymin': ymin,
            'xmax': xmax,
            'ymax': ymax,
            'confidence': confidence,
            'class': class_label
        }

        # Append the dictionary to the data list
        data.append(box_data)
    
    df = pd.DataFrame(data)

    counter+= 1
    if(time.time() - start_time) > x:
        fps = "Fps:"+ str(int(counter/(time.time() - start_time)))
        fps_label.config(text=fps)
        counter = 0
        start_time = time.time()



    for i in range(0,10):
        try:
            xmin = int(df.iloc[i,0])
            ymin = int(df.iloc[i,1])
            xmax = int(df.iloc[i,2])
            ymax = int(df.iloc[i,3])

            centerX = (xmax-xmin)/2+xmin 
            centerY = (ymax-ymin)/2+ymin

            distance = math.dist([centerX,centerY],screenshot_center)

            if int(distance) < closest_part_distance:
                closest_part_distance = distance
                closest_part = i
            
            '''
            color = (255, 0, 0)
            alpha = 0.4
            rectangle_mask = screenshot.copy()
            cv2.rectangle(rectangle_mask,(xmin,ymin),(xmax,ymax), color, -1)
            cv2.addWeighted(rectangle_mask, alpha, screenshot, 1 - alpha, 0, screenshot)
            '''
            
        except:
            print("",end="")



    if keyboard.is_pressed('f10'):
        if triggerbot_toggle[0]:
            triggerbot = not triggerbot
            trigger_label.config(text="Trigger Bot: Active" if triggerbot else "Trigger Bot: Inactive", fg='green' if triggerbot else 'red')
            triggerbot_toggle[0] = False
            threading.Thread(target=cooldown, args=(triggerbot_toggle, 0.2)).start()

    
    if keyboard.is_pressed('f9'):
        if aim_assist_toggle[0] == True:
            aim_assist = not aim_assist
            if aim_assist:
                assist_label.config(text = "Aim Assist: Active", fg= 'green')
            else:
                assist_label.config(text = "Aim Assist: Unactive", fg= 'red')
            print(aim_assist)
            aim_assist_toggle[0] = False
            thread = threading.Thread(target=cooldown, args=(aim_assist_toggle,0.2,))
            thread.start()
    
    if keyboard.is_pressed('f8'):
        if silent_toggle[0] == True:
            silent_aim = not silent_aim
            if silent_aim:
                silent_label.config(text = "Silent Aim: Active", fg= 'green')
            else:
                silent_label.config(text = "Silent Aim: Unactive", fg= 'red')
            print(silent_aim)
            silent_toggle[0] = False
            thread = threading.Thread(target=cooldown, args=(silent_toggle,0.2,))
            thread.start()

    elif keyboard.is_pressed('up') and no_fov_cooldown[0] == True:
        activation_range += 5
        fov_label.config(text=f"FOV: {activation_range}")
        no_fov_cooldown[0] = False
        thread = threading.Thread(target=cooldown, args=(no_fov_cooldown,0.05,))
        thread.start()

    elif keyboard.is_pressed('down') and no_fov_cooldown[0] == True:
        activation_range -= 5
        fov_label.config(text=f"FOV: {activation_range}")
        no_fov_cooldown[0] = False
        thread = threading.Thread(target=cooldown, args=(no_fov_cooldown,0.05,))
        thread.start()

    if closest_part != -1:
        xmin = df.iloc[closest_part,0]
        ymin = df.iloc[closest_part,1]
        xmax = df.iloc[closest_part,2]
        ymax = df.iloc[closest_part,3]

        head_center_list = [(xmax-xmin)/2+xmin,(ymax-ymin)/2+ymin]

        
        if triggerbot == True and screenshot_center[0] in range(int(xmin),int(xmax)) and screenshot_center[1] in range(int(ymin),int(ymax)) and mouse.is_pressed(button='right'):
            serialcomm.write("shoot".encode())
        
        if silent_aim == True and silent_aim_not_cooldown[0] == True and win32api.GetAsyncKeyState(0x06)&0x8000 > 0:
            xdif = (head_center_list[0]-screenshot_center[0])*AIM_SPEED*target_multiply[MONITOR_SCALE]
            ydif = (head_center_list[1]-screenshot_center[1])*AIM_SPEED*target_multiply[MONITOR_SCALE]
            data = f"silent{int(xdif)}:{int(ydif)}"
            serialcomm.write(data.encode())
            silent_aim_not_cooldown[0] = False
            thread = threading.Thread(target=cooldown, args=(silent_aim_not_cooldown,0.2,))
            thread.start()

        #if aim_assist == True and closest_part_distance < 100 and send_next[0] == True and win32api.GetAsyncKeyState(0x06)&0x8000 > 0:
        if aim_assist == True and closest_part_distance < 65 and send_next[0] == True and win32api.GetAsyncKeyState(0x01)&0x8000 > 0:
            xdif = (head_center_list[0] - screenshot_center[0])*AIM_SPEED*target_multiply[MONITOR_SCALE]
            ydif = (head_center_list[1] - screenshot_center[1])*AIM_SPEED*target_multiply[MONITOR_SCALE]
            if abs(xdif) > 20:
                xdif *= 0.4
            else:
                xdif *= 0.4
            if abs(ydif) > 20:
                ydif *= 0.2
            else:
                ydif *= 0.2
            data = f"{int(xdif)}:{int(ydif)}"
            serialcomm.write(data.encode())
            send_next[0] = False
            thread = threading.Thread(target=cooldown, args=(send_next,0.0125,))
            thread.start()
    
    