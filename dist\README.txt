# Silence Rust - Standalone Distribution

This folder contains the standalone release builds of the Silence Rust applications.

## Files Included:
- `action.exe` - Action application (4.2MB) - No console window
- `capture.exe` - Capture application (4.1MB) - No console window
- `action_config.toml` - Configuration for action app
- `capture_config.toml` - Configuration for capture app
- `config.toml` - General configuration
- `run_action.bat` - Launch action app
- `run_capture.bat` - Launch capture app  
- `run_both.bat` - Launch both apps

## Requirements:
- Windows x64 operating system
- No additional dependencies required
- Applications are fully self-contained

## Usage:
1. Double-click `action.exe` for the action application (no console window)
2. Double-click `capture.exe` for the screen capture application (no console window)
3. Use the batch files for convenient launching
4. Both applications will start with a clean GUI interface

## Key Features:
- **No Console Windows**: Applications run as Windows GUI apps without console
- **Fully Standalone**: No additional libraries or runtime required
- **Optimized Performance**: Maximum compiler optimizations applied
- **Small Size**: Compact 4MB executables with all dependencies included

## Optimization Features:
- Link-time optimization (LTO) enabled
- Maximum optimization level (opt-level = 3)
- Debug symbols stripped for smaller file size
- Panic handling optimized for release
- Windows subsystem configuration (no console)

## Distribution:
These executables can be copied to any Windows PC and will run without requiring Rust, Visual Studio, or any additional libraries to be installed.

Created: June 24, 2025
