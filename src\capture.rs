#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use eframe::egui;
use egui::TextureHandle;
use parking_lot::Mutex;
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use std::thread;
use std::time::{Duration, Instant};
use std::net::UdpSocket;
use windows::core::*;
use windows::Win32::Foundation::*;
use windows::Win32::Graphics::Direct3D::*;
use windows::Win32::Graphics::Direct3D11::*;
use windows::Win32::Graphics::Dxgi::*;
use windows::Win32::Graphics::Dxgi::Common::*;
use windows::Win32::System::Threading::*;
use windows::Win32::UI::WindowsAndMessaging::*;
use serde::{Deserialize, Serialize};
use serde_json;
use anyhow::{Result, Context};
use rayon::prelude::*;
use aes::Aes256;
use aes::cipher::{BlockEncrypt, BlockDecrypt, KeyInit, generic_array::GenericArray};
use rand::RngCore;
use base64::{Engine as _, engine::general_purpose};

mod network_discovery;
use network_discovery::{NetworkDiscovery, DeviceType, ConnectionStatus};

// Color presets based on main.py reference
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ColorPreset {
    Yellow,        // [30, 125, 150] - [30, 255, 255] in OpenCV HSV
    Yellow2,       // [30, 170, 254] - [30, 230, 255] in OpenCV HSV
    Purple,        // [144, 72, 150] - [152, 255, 255] in OpenCV HSV
    AntiAstra,     // [140, 86, 172] - [150, 255, 255] in OpenCV HSV
    Red,           // [0, 170, 150] - [5, 255, 255] in OpenCV HSV
    Custom,
}

impl Default for ColorPreset {
    fn default() -> Self {
        ColorPreset::Purple
    }
}

impl ColorPreset {
    pub fn to_hsv_range(&self) -> ([f32; 3], [f32; 3]) {
        match self {
            ColorPreset::Yellow => (
                [0.15000000596046448, 0.41499999165534973, 0.7300000190734863],
                [0.17000000178813934, 1.0, 1.0]
            ),
            ColorPreset::Yellow2 => (
                Self::opencv_to_rust_hsv([30.0, 170.0, 254.0]),
                Self::opencv_to_rust_hsv([30.0, 230.0, 255.0])
            ),
            ColorPreset::Purple => (
                Self::opencv_to_rust_hsv([144.0, 72.0, 150.0]),
                Self::opencv_to_rust_hsv([152.0, 255.0, 255.0])
            ),
            ColorPreset::AntiAstra => (
                Self::opencv_to_rust_hsv([140.0, 86.0, 172.0]),
                Self::opencv_to_rust_hsv([150.0, 255.0, 255.0])
            ),
            ColorPreset::Red => (
                Self::opencv_to_rust_hsv([0.0, 170.0, 150.0]),
                Self::opencv_to_rust_hsv([5.0, 255.0, 255.0])
            ),
            ColorPreset::Custom => (
                [0.0, 0.0, 0.0],
                [1.0, 1.0, 1.0]
            ),
        }
    }
    
    // Convert OpenCV HSV (H: 0-179, S: 0-255, V: 0-255) to Rust HSV (H: 0-1, S: 0-1, V: 0-1)
    fn opencv_to_rust_hsv(opencv_hsv: [f32; 3]) -> [f32; 3] {
        [
            opencv_hsv[0] / 179.0,  // H: 0-179 -> 0-1
            opencv_hsv[1] / 255.0,  // S: 0-255 -> 0-1
            opencv_hsv[2] / 255.0,  // V: 0-255 -> 0-1
        ]
    }
    
    pub fn name(&self) -> &'static str {
        match self {
            ColorPreset::Yellow => "Yellow",
            ColorPreset::Yellow2 => "Yellow 2",
            ColorPreset::Purple => "Purple",
            ColorPreset::AntiAstra => "Anti Astra",
            ColorPreset::Red => "Red",
            ColorPreset::Custom => "Custom",
        }
    }
}

// Default encryption key (256-bit)
const DEFAULT_ENCRYPTION_KEY: &str = "SilenceCapture2024KeyForSecureUDP!!";

// Encryption utilities
struct CryptoUtils;

impl CryptoUtils {
    // Pad data to AES block size (16 bytes)
    fn pad_data(data: &[u8]) -> Vec<u8> {
        let mut padded = data.to_vec();
        let padding_needed = 16 - (data.len() % 16);
        if padding_needed != 16 {
            padded.extend(vec![padding_needed as u8; padding_needed]);
        }
        padded
    }
    
    // Remove padding from decrypted data
    fn unpad_data(data: &[u8]) -> Result<Vec<u8>> {
        if data.is_empty() {
            return Err(anyhow::anyhow!("Empty data cannot be unpadded"));
        }
        
        let padding_size = *data.last().unwrap() as usize;
        if padding_size == 0 || padding_size > 16 || padding_size > data.len() {
            return Err(anyhow::anyhow!("Invalid padding"));
        }
        
        // Verify padding bytes
        for &byte in &data[data.len() - padding_size..] {
            if byte != padding_size as u8 {
                return Err(anyhow::anyhow!("Invalid padding bytes"));
            }
        }
        
        Ok(data[..data.len() - padding_size].to_vec())
    }
    
    // Encrypt data using AES-256
    fn encrypt(data: &str, key: &str) -> Result<String> {
        // Create 256-bit key from provided key string
        let mut key_bytes = [0u8; 32];
        let key_data = key.as_bytes();
        let copy_len = key_data.len().min(32);
        key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
        
        let cipher = Aes256::new(GenericArray::from_slice(&key_bytes));
        let padded_data = Self::pad_data(data.as_bytes());
        
        let mut encrypted = Vec::new();
        
        // Encrypt in 16-byte blocks
        for chunk in padded_data.chunks(16) {
            let mut block = GenericArray::clone_from_slice(chunk);
            cipher.encrypt_block(&mut block);
            encrypted.extend_from_slice(&block);
        }
        
        // Generate random IV (16 bytes)
        let mut iv = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut iv);
        
        // Prepend IV to encrypted data
        let mut result = iv.to_vec();
        result.extend(encrypted);
        
        // Encode as base64
        Ok(general_purpose::STANDARD.encode(&result))
    }
    
    // Decrypt data using AES-256
    fn decrypt(encrypted_data: &str, key: &str) -> Result<String> {
        // Decode from base64
        let data = general_purpose::STANDARD.decode(encrypted_data)
            .context("Failed to decode base64")?;
        
        if data.len() < 32 { // At least IV (16) + one block (16)
            return Err(anyhow::anyhow!("Encrypted data too short"));
        }
        
        // Create 256-bit key from provided key string
        let mut key_bytes = [0u8; 32];
        let key_data = key.as_bytes();
        let copy_len = key_data.len().min(32);
        key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
        
        let cipher = Aes256::new(GenericArray::from_slice(&key_bytes));
        
        // Extract IV and encrypted data
        let _iv = &data[0..16]; // IV for future use if needed
        let encrypted = &data[16..];
        
        let mut decrypted = Vec::new();
        
        // Decrypt in 16-byte blocks
        for chunk in encrypted.chunks(16) {
            if chunk.len() == 16 {
                let mut block = GenericArray::clone_from_slice(chunk);
                cipher.decrypt_block(&mut block);
                decrypted.extend_from_slice(&block);
            }
        }
        
        // Remove padding
        let unpadded = Self::unpad_data(&decrypted)?;
        
        // Convert to string
        String::from_utf8(unpadded).context("Failed to convert decrypted data to string")
    }
}

// UDP Message structure for communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TargetMessage {
    pub center_x: f32,
    pub center_y: f32,
    pub confidence: f32,
    pub capture_size: u32,
    pub message_type: String, // "aim", "trigger", or "clear"
    pub timestamp: u64, // Timestamp in milliseconds
}

// Configuration structures for TOML parsing
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CaptureConfig {
    pub aim_fov: u32,
    pub trigger_fov: u32,
    pub udp_port: u16,
    pub target_ip: String,
    pub enable_encryption: bool,
    pub encryption_key: String,
    pub color_preset: ColorPreset,
    pub custom_hsv_lower: [f32; 3],
    pub custom_hsv_upper: [f32; 3],
}

impl Default for CaptureConfig {
    fn default() -> Self {
        Self {
            aim_fov: 65,
            trigger_fov: 8,
            udp_port: 12346,
            target_ip: "127.0.0.1:12345".to_string(),
            enable_encryption: true,
            encryption_key: DEFAULT_ENCRYPTION_KEY.to_string(),
            color_preset: ColorPreset::Purple,
            custom_hsv_lower: [0.8, 0.282, 0.588],
            custom_hsv_upper: [0.844, 1.0, 1.0],
        }
    }
}

impl CaptureConfig {
    pub fn load_from_file() -> Self {
        match std::fs::read_to_string("capture_config.toml") {
            Ok(content) => {
                match toml::from_str::<CaptureConfig>(&content) {
                    Ok(config) => config,
                    Err(_) => Self::default()
                }
            }
            Err(_) => Self::default()
        }
    }

    pub fn save_to_file(&self) -> Result<()> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write("capture_config.toml", content)?;
        Ok(())
    }
    
    pub fn get_current_hsv_range(&self) -> ([f32; 3], [f32; 3]) {
        match self.color_preset {
            ColorPreset::Custom => (self.custom_hsv_lower, self.custom_hsv_upper),
            _ => self.color_preset.to_hsv_range(),
        }
    }
}

// Target detection result
#[derive(Debug, Clone)]
pub struct TargetInfo {
    pub center_x: f32,
    pub center_y: f32,
    pub confidence: f32,
}

#[derive(Clone)]
pub struct FrameData {
    pub pixels: Vec<egui::Color32>,
    pub filtered_pixels: Vec<egui::Color32>,
    pub fps: f32,
}

// Convert RGB to HSV (optimized)
fn rgb_to_hsv(r: f32, g: f32, b: f32) -> [f32; 3] {
    let max = r.max(g.max(b));
    let min = r.min(g.min(b));
    let delta = max - min;
    
    let v = max;
    let s = if max == 0.0 { 0.0 } else { delta / max };
    
    let h = if delta == 0.0 {
        0.0
    } else if max == r {
        ((g - b) / delta) % 6.0
    } else if max == g {
        (b - r) / delta + 2.0
    } else {
        (r - g) / delta + 4.0
    };
    
    [h / 6.0, s, v]
}

// Check if HSV values are within the target range (optimized)
fn is_target_color(hsv: [f32; 3], hsv_lower: &[f32; 3], hsv_upper: &[f32; 3]) -> bool {
    let [h, s, v] = hsv;
    
    if s < 0.1 || v < 0.1 {
        return false;
    }
    
    h >= hsv_lower[0] && h <= hsv_upper[0] &&
    s >= hsv_lower[1] && s <= hsv_upper[1] &&
    v >= hsv_lower[2] && v <= hsv_upper[2]
}

// Apply color filtering using parallel processing
fn apply_color_filter(pixels: &[egui::Color32], hsv_lower: &[f32; 3], hsv_upper: &[f32; 3]) -> Vec<egui::Color32> {
    pixels.par_iter().map(|&pixel| {
        let r = pixel.r() as f32 / 255.0;
        let g = pixel.g() as f32 / 255.0;
        let b = pixel.b() as f32 / 255.0;
        
        let hsv = rgb_to_hsv(r, g, b);
        
        if is_target_color(hsv, hsv_lower, hsv_upper) {
            pixel
        } else {
            egui::Color32::from_rgba_premultiplied(
                (pixel.r() as f32 * 0.1) as u8,
                (pixel.g() as f32 * 0.1) as u8,
                (pixel.b() as f32 * 0.1) as u8,
                pixel.a(),
            )
        }
    }).collect()
}

struct DirectXCapture {
    context: ID3D11DeviceContext,
    duplication: IDXGIOutputDuplication,
    staging_texture: ID3D11Texture2D,
    center_x: u32,
    center_y: u32,
    capture_size: u32,
}

impl DirectXCapture {
    fn new(capture_size: u32) -> windows::core::Result<Self> {
        unsafe {
            let screen_width = GetSystemMetrics(SM_CXSCREEN) as u32;
            let screen_height = GetSystemMetrics(SM_CYSCREEN) as u32;
            let center_x = (screen_width / 2) - (capture_size / 2);
            let center_y = (screen_height / 2) - (capture_size / 2);

            let mut device: Option<ID3D11Device> = None;
            let mut context: Option<ID3D11DeviceContext> = None;
            
            D3D11CreateDevice(
                None,
                D3D_DRIVER_TYPE_HARDWARE,
                HMODULE::default(),
                D3D11_CREATE_DEVICE_FLAG(0),
                None,
                D3D11_SDK_VERSION,
                Some(&mut device),
                None,
                Some(&mut context),
            )?;

            let device = device.unwrap();
            let context = context.unwrap();

            let dxgi_device: IDXGIDevice = device.cast()?;
            let adapter = dxgi_device.GetAdapter()?;
            let output = adapter.EnumOutputs(0)?;
            let output1: IDXGIOutput1 = output.cast()?;

            let duplication = output1.DuplicateOutput(&device)?;

            let texture_desc = D3D11_TEXTURE2D_DESC {
                Width: capture_size,
                Height: capture_size,
                MipLevels: 1,
                ArraySize: 1,
                Format: DXGI_FORMAT_B8G8R8A8_UNORM,
                SampleDesc: DXGI_SAMPLE_DESC {
                    Count: 1,
                    Quality: 0,
                },
                Usage: D3D11_USAGE_STAGING,
                BindFlags: 0,
                CPUAccessFlags: D3D11_CPU_ACCESS_READ.0 as u32,
                MiscFlags: 0,
            };

            let mut staging_texture: Option<ID3D11Texture2D> = None;
            device.CreateTexture2D(&texture_desc, None, Some(&mut staging_texture))?;
            let staging_texture = staging_texture.unwrap();

            Ok(Self {
                context,
                duplication,
                staging_texture,
                center_x,
                center_y,
                capture_size,
            })
        }
    }

    fn capture_directx(&self) -> windows::core::Result<Option<Vec<egui::Color32>>> {
        unsafe {
            let mut frame_info = DXGI_OUTDUPL_FRAME_INFO::default();
            let mut desktop_resource: Option<IDXGIResource> = None;
            
            match self.duplication.AcquireNextFrame(0, &mut frame_info, &mut desktop_resource) {
                Ok(_) => {
                    if let Some(resource) = desktop_resource {
                        let desktop_texture: ID3D11Texture2D = resource.cast()?;

                        let src_box = D3D11_BOX {
                            left: self.center_x,
                            top: self.center_y,
                            front: 0,
                            right: self.center_x + self.capture_size,
                            bottom: self.center_y + self.capture_size,
                            back: 1,
                        };

                        self.context.CopySubresourceRegion(
                            &self.staging_texture,
                            0,
                            0,
                            0,
                            0,
                            &desktop_texture,
                            0,
                            Some(&src_box),
                        );

                        let mut mapped = D3D11_MAPPED_SUBRESOURCE::default();
                        self.context.Map(&self.staging_texture, 0, D3D11_MAP_READ, 0, Some(&mut mapped))?;

                        let mut pixels = Vec::with_capacity((self.capture_size * self.capture_size) as usize);
                        
                        let row_pitch = mapped.RowPitch as usize;
                        let bytes_per_pixel = 4;
                        let row_width_bytes = self.capture_size as usize * bytes_per_pixel;
                        
                        for row in 0..self.capture_size as usize {
                            let row_start = row * row_pitch;
                            let row_data = std::slice::from_raw_parts(
                                (mapped.pData as *const u8).add(row_start),
                                row_width_bytes,
                            );
                            
                            for chunk in row_data.chunks_exact(4) {
                                pixels.push(egui::Color32::from_rgba_premultiplied(
                                    chunk[2], // R
                                    chunk[1], // G
                                    chunk[0], // B
                                    chunk[3], // A
                                ));
                            }
                        }

                        self.context.Unmap(&self.staging_texture, 0);
                        let _ = self.duplication.ReleaseFrame();

                        Ok(Some(pixels))
                    } else {
                        Err(windows::core::Error::from(E_FAIL))
                    }
                }
                Err(e) => {
                    if e.code() == DXGI_ERROR_WAIT_TIMEOUT {
                        Ok(None)
                    } else {
                        Err(e)
                    }
                }
            }
        }
    }
}

pub struct TargetDetector {
    udp_socket: UdpSocket,
    config: Arc<Mutex<CaptureConfig>>,
}

impl TargetDetector {
    pub fn new(config: Arc<Mutex<CaptureConfig>>) -> Result<Self> {
        let socket = UdpSocket::bind(format!("0.0.0.0:{}", config.lock().udp_port))?;
        socket.set_nonblocking(true)?;
        
        Ok(Self {
            udp_socket: socket,
            config,
        })
    }
    
    pub fn detect_target_in_pixels(&self, pixels: &[egui::Color32], fov: u32, hsv_lower: &[f32; 3], hsv_upper: &[f32; 3]) -> Option<TargetInfo> {
        let mut total_weight = 0.0;
        let mut weighted_x = 0.0;
        let mut weighted_y = 0.0;
        let mut target_count = 0;
        
        for (i, &pixel) in pixels.iter().enumerate() {
            let x = (i % fov as usize) as f32;
            let y = (i / fov as usize) as f32;
            
            let r = pixel.r() as f32 / 255.0;
            let g = pixel.g() as f32 / 255.0;
            let b = pixel.b() as f32 / 255.0;
            
            let hsv = rgb_to_hsv(r, g, b);
            
            if is_target_color(hsv, hsv_lower, hsv_upper) {
                target_count += 1;
                let weight = (r + g + b) / 3.0;
                total_weight += weight;
                weighted_x += x * weight;
                weighted_y += y * weight;
            }
        }
        
        if total_weight > 0.0 && target_count > 5 {
            let center_x = weighted_x / total_weight;
            let center_y = weighted_y / total_weight;
            let confidence = (target_count as f32 / (fov * fov) as f32).min(1.0);
            
            Some(TargetInfo {
                center_x,
                center_y,
                confidence,
            })
        } else {
            None
        }
    }    pub fn send_target_data(&self, target: &TargetInfo, capture_size: u32, message_type: &str) -> Result<()> {
        let message = TargetMessage {
            center_x: target.center_x,
            center_y: target.center_y,
            confidence: target.confidence,
            capture_size,
            message_type: message_type.to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        };
        
        let serialized = serde_json::to_string(&message)?;
        let config = self.config.lock();
        
        // Encrypt the message if encryption is enabled
        let data_to_send = if config.enable_encryption {
            match CryptoUtils::encrypt(&serialized, &config.encryption_key) {
                Ok(encrypted) => encrypted.into_bytes(),
                Err(e) => return Err(e),
            }
        } else {
            serialized.into_bytes()
        };
        
        self.udp_socket.send_to(&data_to_send, &config.target_ip)?;
        Ok(())
    }    pub fn send_clear_message(&self) -> Result<()> {
        let message = TargetMessage {
            center_x: 0.0,
            center_y: 0.0,
            confidence: 0.0,
            capture_size: 0,
            message_type: "clear".to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
        };
        
        let serialized = serde_json::to_string(&message)?;
        let config = self.config.lock();
        
        // Encrypt the message if encryption is enabled
        let data_to_send = if config.enable_encryption {
            match CryptoUtils::encrypt(&serialized, &config.encryption_key) {
                Ok(encrypted) => encrypted.into_bytes(),
                Err(e) => return Err(e),
            }
        } else {
            serialized.into_bytes()
        };
        
        self.udp_socket.send_to(&data_to_send, &config.target_ip)?;
        Ok(())
    }
    
    // Method to receive and decrypt UDP messages
    pub fn receive_message(&self) -> Result<Option<TargetMessage>> {
        let mut buffer = [0u8; 1024];
        
        match self.udp_socket.recv_from(&mut buffer) {
            Ok((size, _addr)) => {
                let received_data = &buffer[..size];
                let config = self.config.lock();
                
                // Decrypt the message if encryption is enabled
                let json_data = if config.enable_encryption {
                    let encrypted_string = String::from_utf8(received_data.to_vec())
                        .context("Failed to convert received data to string")?;
                    CryptoUtils::decrypt(&encrypted_string, &config.encryption_key)?
                } else {
                    String::from_utf8(received_data.to_vec())
                        .context("Failed to convert received data to string")?
                };
                
                let message: TargetMessage = serde_json::from_str(&json_data)
                    .context("Failed to deserialize message")?;
                
                Ok(Some(message))
            }
            Err(e) => {
                if e.kind() == std::io::ErrorKind::WouldBlock {
                    Ok(None) // No message available
                } else {
                    Err(anyhow::anyhow!("UDP receive error: {}", e))
                }
            }
        }
    }
}

pub struct ScreenCaptureApp {
    texture: Option<TextureHandle>,
    filtered_texture: Option<TextureHandle>,
    frame_data: Arc<Mutex<Option<FrameData>>>,
    current_fps: f32,
    last_texture_update: Instant,
    texture_update_interval: Duration,
    show_filtered: bool,
    config: Arc<Mutex<CaptureConfig>>,
    current_capture_size: u32,
    is_running: Arc<AtomicBool>,
    show_preview: bool,
    capture_enabled: bool,
    target_detection_enabled: bool,
    network_discovery: Option<NetworkDiscovery>,
    show_network_panel: bool,
    selected_device: Option<String>,
}

impl ScreenCaptureApp {
    pub fn new(_cc: &eframe::CreationContext<'_>) -> Self {
        let frame_data = Arc::new(Mutex::new(None));
        let frame_data_clone = frame_data.clone();
        
        let config = Arc::new(Mutex::new(CaptureConfig::load_from_file()));
        let config_clone = config.clone();

        let is_running = Arc::new(AtomicBool::new(true));
        let is_running_clone = is_running.clone();

        // Start optimized DirectX capture thread
        thread::spawn(move || {
            unsafe {
                let handle = GetCurrentThread();
                let _ = SetThreadPriority(handle, THREAD_PRIORITY_TIME_CRITICAL);
            }
            
            capture_loop_optimized(frame_data_clone, config_clone, is_running_clone);
        });

        let initial_capture_size = config.lock().aim_fov;
        
        // Initialize network discovery
        let mut network_discovery = NetworkDiscovery::new().ok();
        if let Some(ref mut discovery) = network_discovery {
            let _ = discovery.start_discovery();
        }
        
        // Start discovery responder
        let config_responder = config.clone();
        thread::spawn(move || {
            Self::start_discovery_responder(config_responder);
        });
        
        Self {
            texture: None,
            filtered_texture: None,
            frame_data,
            current_fps: 0.0,
            last_texture_update: Instant::now(),
            texture_update_interval: Duration::from_millis(16),
            show_filtered: true,
            config,
            current_capture_size: initial_capture_size,
            is_running,
            show_preview: true,
            capture_enabled: true,
            target_detection_enabled: true,
            network_discovery,
            show_network_panel: false,
            selected_device: None,
        }
    }
    
    fn start_discovery_responder(config: Arc<Mutex<CaptureConfig>>) {
        if let Ok(socket) = UdpSocket::bind("0.0.0.0:5003") {
            socket.set_nonblocking(true).ok();
            
            loop {
                let mut buffer = [0u8; 1024];
                if let Ok((size, addr)) = socket.recv_from(&mut buffer) {
                    if let Ok(msg) = serde_json::from_slice::<serde_json::Value>(&buffer[..size]) {
                        if msg.get("device_type").and_then(|v| v.as_str()) == Some("scanner") {
                            let response = serde_json::json!({
                                "device_type": "capture",
                                "device_name": "Screen Capture Device",
                                "version": "1.0",
                                "status": "active",
                                "port": config.lock().udp_port
                            });
                            
                            if let Ok(response_data) = serde_json::to_vec(&response) {
                                let _ = socket.send_to(&response_data, addr);
                            }
                        }
                    }
                }
                thread::sleep(Duration::from_millis(10));
            }
        }
    }
}

impl eframe::App for ScreenCaptureApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Handle frame data updates only when preview is enabled
        if self.show_preview && self.capture_enabled {
            if let Some(data) = self.frame_data.lock().take() {
                self.current_fps = data.fps;

                if self.last_texture_update.elapsed() >= self.texture_update_interval {
                    let config = self.config.lock();
                    if config.aim_fov != self.current_capture_size {
                        self.current_capture_size = config.aim_fov;
                    }
                    drop(config);
                    
                    let color_image = egui::ColorImage {
                        size: [self.current_capture_size as usize, self.current_capture_size as usize],
                        pixels: data.pixels,
                    };

                    let filtered_color_image = egui::ColorImage {
                        size: [self.current_capture_size as usize, self.current_capture_size as usize],
                        pixels: data.filtered_pixels,
                    };

                    if let Some(ref mut texture) = self.texture {
                        texture.set(color_image, egui::TextureOptions::NEAREST);
                    } else {
                        self.texture = Some(ctx.load_texture(
                            "screen_capture",
                            color_image,
                            egui::TextureOptions::NEAREST,
                        ));
                    }

                    if let Some(ref mut filtered_texture) = self.filtered_texture {
                        filtered_texture.set(filtered_color_image, egui::TextureOptions::NEAREST);
                    } else {
                        self.filtered_texture = Some(ctx.load_texture(
                            "screen_capture_filtered",
                            filtered_color_image,
                            egui::TextureOptions::NEAREST,
                        ));
                    }

                    self.last_texture_update = Instant::now();
                }
            }
        }

        // Handle keyboard shortcuts
        ctx.input(|i| {
            if i.key_pressed(egui::Key::F9) {
                self.show_preview = !self.show_preview;
            }
            
            if i.key_pressed(egui::Key::F10) {
                self.capture_enabled = !self.capture_enabled;
            }
            
            if i.key_pressed(egui::Key::F11) {
                self.target_detection_enabled = !self.target_detection_enabled;
            }
        });

        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("📸 Screen Capture Engine");
            
            ui.horizontal(|ui| {
                if self.capture_enabled {
                    ui.colored_label(egui::Color32::GREEN, format!("FPS: {:.0}", self.current_fps));
                } else {
                    ui.colored_label(egui::Color32::GRAY, "FPS: STOPPED");
                }
                
                // Preview toggle button
                let preview_button_text = if self.show_preview { 
                    "⏸️ Hide Preview" 
                } else { 
                    "▶️ Show Preview" 
                };
                let preview_button_color = if self.show_preview { 
                    egui::Color32::YELLOW 
                } else { 
                    egui::Color32::GREEN 
                };
                
                if ui.add(egui::Button::new(preview_button_text).fill(preview_button_color)).clicked() {
                    self.show_preview = !self.show_preview;
                }
                
                if ui.button("🛑 Stop All").clicked() {
                    self.is_running.store(false, Ordering::Relaxed);
                    std::process::exit(0);
                }
            });

            // Capture controls
            ui.horizontal(|ui| {
                ui.checkbox(&mut self.capture_enabled, "📸 Enable Capture");
                ui.checkbox(&mut self.target_detection_enabled, "🎯 Target Detection");
                ui.checkbox(&mut self.show_preview, "📺 Show Preview");
                
                // Network discovery toggle
                if ui.button(if self.show_network_panel { "🌐 Hide Network" } else { "🌐 Show Network" }).clicked() {
                    self.show_network_panel = !self.show_network_panel;
                }
            });
            
            // Color Selection - Made prominent and visible
            ui.separator();
            ui.horizontal(|ui| {
                ui.strong("🎨 Color Target:");
                
                let mut config = self.config.lock();
                let mut current_preset = config.color_preset.clone();
                
                egui::ComboBox::from_label("")
                    .selected_text(format!("🎯 {}", current_preset.name()))
                    .width(120.0)
                    .show_ui(ui, |ui| {
                        ui.selectable_value(&mut current_preset, ColorPreset::Yellow, "🟡 Yellow");
                        ui.selectable_value(&mut current_preset, ColorPreset::Yellow2, "🟨 Yellow 2");
                        ui.selectable_value(&mut current_preset, ColorPreset::Purple, "🟣 Purple");
                        ui.selectable_value(&mut current_preset, ColorPreset::AntiAstra, "🔮 Anti Astra");
                        ui.selectable_value(&mut current_preset, ColorPreset::Red, "🔴 Red");
                        ui.selectable_value(&mut current_preset, ColorPreset::Custom, "⚙️ Custom");
                    });
                
                if current_preset != config.color_preset {
                    config.color_preset = current_preset.clone();
                    let _ = config.save_to_file(); // Auto-save when preset changes
                }
                
                // Show current HSV range info
                let (hsv_lower, hsv_upper) = config.get_current_hsv_range();
                ui.label(format!("H:{:.2}-{:.2}", hsv_lower[0], hsv_upper[0]));
                ui.label(format!("S:{:.2}-{:.2}", hsv_lower[1], hsv_upper[1]));
                ui.label(format!("V:{:.2}-{:.2}", hsv_lower[2], hsv_upper[2]));
                
                if ui.button("💾 Save").clicked() {
                    let _ = config.save_to_file();
                }
            });
            
            // Custom color sliders - shown immediately when Custom is selected
            if self.config.lock().color_preset == ColorPreset::Custom {
                ui.separator();
                ui.horizontal(|ui| {
                    ui.strong("🎛️ Custom HSV Range:");
                });
                
                let mut config = self.config.lock();
                let mut settings_changed = false;
                
                // Lower bounds
                ui.horizontal(|ui| {
                    ui.label("Lower:");
                    ui.label("H:");
                    if ui.add(egui::Slider::new(&mut config.custom_hsv_lower[0], 0.0..=1.0)
                        .step_by(0.001)
                        .fixed_decimals(3)
                        .custom_formatter(|n, _| format!("{:.3}", n))).changed() {
                        settings_changed = true;
                    }
                    ui.label("S:");
                    if ui.add(egui::Slider::new(&mut config.custom_hsv_lower[1], 0.0..=1.0)
                        .step_by(0.001)
                        .fixed_decimals(3)
                        .custom_formatter(|n, _| format!("{:.3}", n))).changed() {
                        settings_changed = true;
                    }
                    ui.label("V:");
                    if ui.add(egui::Slider::new(&mut config.custom_hsv_lower[2], 0.0..=1.0)
                        .step_by(0.001)
                        .fixed_decimals(3)
                        .custom_formatter(|n, _| format!("{:.3}", n))).changed() {
                        settings_changed = true;
                    }
                });
                
                // Upper bounds
                ui.horizontal(|ui| {
                    ui.label("Upper:");
                    ui.label("H:");
                    if ui.add(egui::Slider::new(&mut config.custom_hsv_upper[0], 0.0..=1.0)
                        .step_by(0.001)
                        .fixed_decimals(3)
                        .custom_formatter(|n, _| format!("{:.3}", n))).changed() {
                        settings_changed = true;
                    }
                    ui.label("S:");
                    if ui.add(egui::Slider::new(&mut config.custom_hsv_upper[1], 0.0..=1.0)
                        .step_by(0.001)
                        .fixed_decimals(3)
                        .custom_formatter(|n, _| format!("{:.3}", n))).changed() {
                        settings_changed = true;
                    }
                    ui.label("V:");
                    if ui.add(egui::Slider::new(&mut config.custom_hsv_upper[2], 0.0..=1.0)
                        .step_by(0.001)
                        .fixed_decimals(3)
                        .custom_formatter(|n, _| format!("{:.3}", n))).changed() {
                        settings_changed = true;
                    }
                });
                
                // Auto-save when custom settings change
                if settings_changed {
                    let _ = config.save_to_file();
                }
                
                ui.horizontal(|ui| {
                    if ui.button("🎨 Reset to Purple").clicked() {
                        let (lower, upper) = ColorPreset::Purple.to_hsv_range();
                        config.custom_hsv_lower = lower;
                        config.custom_hsv_upper = upper;
                        let _ = config.save_to_file();
                    }
                    if ui.button("🟡 Reset to Yellow").clicked() {
                        let (lower, upper) = ColorPreset::Yellow.to_hsv_range();
                        config.custom_hsv_lower = lower;
                        config.custom_hsv_upper = upper;
                        let _ = config.save_to_file();
                    }
                    if ui.button("🔴 Reset to Red").clicked() {
                        let (lower, upper) = ColorPreset::Red.to_hsv_range();
                        config.custom_hsv_lower = lower;
                        config.custom_hsv_upper = upper;
                        let _ = config.save_to_file();
                    }
                });
            }

            // Status information
            ui.horizontal(|ui| {
                if self.capture_enabled {
                    ui.colored_label(egui::Color32::GREEN, "📸 CAPTURE: ACTIVE");
                } else {
                    ui.colored_label(egui::Color32::GRAY, "📸 CAPTURE: STOPPED");
                }
                
                if self.target_detection_enabled {
                    ui.colored_label(egui::Color32::GREEN, "🎯 DETECTION: ACTIVE");
                } else {
                    ui.colored_label(egui::Color32::GRAY, "🎯 DETECTION: OFF");
                }
                
                // Network status
                if let Some(ref discovery) = self.network_discovery {
                    if discovery.is_scanning() {
                        ui.colored_label(egui::Color32::BLUE, "🌐 SCANNING");
                    } else {
                        ui.colored_label(egui::Color32::GRAY, "🌐 IDLE");
                    }
                    
                    if let Some(local_ip) = discovery.get_local_ip_string() {
                        ui.colored_label(egui::Color32::LIGHT_BLUE, format!("📡 Local IP: {}", local_ip));
                    }
                }
            });

            // Network Discovery Panel
            if self.show_network_panel {
                ui.collapsing("🌐 Network Discovery", |ui| {
                    if let Some(ref mut discovery) = self.network_discovery {
                        ui.horizontal(|ui| {
                            if ui.button(if discovery.is_scanning() { "⏹️ Stop Scan" } else { "🔍 Start Scan" }).clicked() {
                                if discovery.is_scanning() {
                                    discovery.stop_discovery();
                                } else {
                                    let _ = discovery.start_discovery();
                                }
                            }
                            
                            ui.label(format!("Devices found: {}", discovery.get_devices().len()));
                        });
                        
                        ui.separator();
                        
                        // Device list
                        egui::ScrollArea::vertical().max_height(200.0).show(ui, |ui| {
                            let devices = discovery.get_devices();
                            if devices.is_empty() {
                                ui.label("No devices found. Click 'Start Scan' to search for devices.");
                            } else {
                                for device in devices {
                                    ui.horizontal(|ui| {
                                        // Device type icon
                                        let icon = match device.device_type {
                                            DeviceType::CaptureDevice => "📸",
                                            DeviceType::ActionDevice => "🎮",
                                            DeviceType::Unknown => "❓",
                                        };
                                        
                                        // Status color
                                        let status_color = match device.status {
                                            ConnectionStatus::Connected => egui::Color32::GREEN,
                                            ConnectionStatus::Disconnected => egui::Color32::RED,
                                            ConnectionStatus::Scanning => egui::Color32::YELLOW,
                                            ConnectionStatus::Error(_) => egui::Color32::from_rgb(255, 165, 0), // Orange
                                        };
                                        
                                        ui.colored_label(status_color, icon);
                                        ui.label(&device.name);
                                        ui.label(format!("({})", device.ip));
                                        
                                        // Status indicator
                                        let status_text = match &device.status {
                                            ConnectionStatus::Connected => "✅ Connected",
                                            ConnectionStatus::Disconnected => "❌ Disconnected",
                                            ConnectionStatus::Scanning => "🔍 Scanning",
                                            ConnectionStatus::Error(msg) => &format!("⚠️ Error: {}", msg),
                                        };
                                        ui.colored_label(status_color, status_text);
                                        
                                        // Response time
                                        if let Some(response_time) = device.response_time {
                                            ui.label(format!("{}ms", response_time.as_millis()));
                                        }
                                        
                                        // Connect/Disconnect button
                                        let button_text = if device.status == ConnectionStatus::Connected {
                                            "Disconnect"
                                        } else {
                                            "Connect"
                                        };
                                        
                                        if ui.button(button_text).clicked() {
                                            if device.status == ConnectionStatus::Connected {
                                                let _ = discovery.disconnect_from_device(&device.ip);
                                            } else {
                                                let _ = discovery.connect_to_device(&device.ip);
                                                self.selected_device = Some(device.ip.clone());
                                                
                                                // Update target IP in config
                                                let mut config = self.config.lock();
                                                config.target_ip = format!("{}:5005", device.ip);
                                                let _ = config.save_to_file();
                                            }
                                        }
                                    });
                                    ui.separator();
                                }
                            }
                        });
                        
                        // Current target information
                        ui.separator();
                        ui.horizontal(|ui| {
                            ui.label("Current Target:");
                            let target_ip = self.config.lock().target_ip.clone();
                            ui.colored_label(egui::Color32::LIGHT_BLUE, &target_ip);
                            
                            if let Some(ref selected) = self.selected_device {
                                ui.label(format!("(Selected: {})", selected));
                            }
                        });
                    } else {
                        ui.colored_label(egui::Color32::RED, "❌ Network discovery failed to initialize");
                        if ui.button("🔄 Retry").clicked() {
                            self.network_discovery = NetworkDiscovery::new().ok();
                            if let Some(ref mut discovery) = self.network_discovery {
                                let _ = discovery.start_discovery();
                            }
                        }
                    }
                });
            }
            
            // Configuration settings
            ui.collapsing("⚙️ Capture Settings", |ui| {
                let mut config = self.config.lock();
                
                ui.horizontal(|ui| {
                    ui.label("Aim FOV:");
                    ui.add(egui::Slider::new(&mut config.aim_fov, 10..=200).suffix("px"));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Trigger FOV:");
                    ui.add(egui::Slider::new(&mut config.trigger_fov, 5..=50).suffix("px"));
                });
                
                ui.horizontal(|ui| {
                    ui.label("UDP Port:");
                    ui.add(egui::DragValue::new(&mut config.udp_port).range(1024..=65535));
                });
                
                ui.horizontal(|ui| {
                    ui.label("Target IP:");
                    ui.text_edit_singleline(&mut config.target_ip);
                });
                
                ui.separator();
                
                ui.horizontal(|ui| {
                    ui.checkbox(&mut config.enable_encryption, "🔒 Enable UDP Encryption");
                });
                
                ui.horizontal(|ui| {
                    ui.label("Encryption Key:");
                    ui.text_edit_singleline(&mut config.encryption_key);
                });
                
                if config.enable_encryption {
                    ui.colored_label(egui::Color32::GREEN, "🔒 UDP messages are encrypted");
                } else {
                    ui.colored_label(egui::Color32::RED, "⚠️ UDP messages are sent in plain text");
                }
                
                if ui.button("💾 Save Config").clicked() {
                    let _ = config.save_to_file();
                }
            });
            
            ui.separator();
            
            // Color detection settings - Advanced options
            ui.collapsing("🔬 Advanced Color Detection Settings", |ui| {
                let config = self.config.lock();
                
                // Show current HSV range in detail
                let (hsv_lower, hsv_upper) = config.get_current_hsv_range();
                ui.separator();
                ui.strong("Current Color Range Details:");
                ui.label(format!("Lower HSV: [{:.3}, {:.3}, {:.3}]", hsv_lower[0], hsv_lower[1], hsv_lower[2]));
                ui.label(format!("Upper HSV: [{:.3}, {:.3}, {:.3}]", hsv_upper[0], hsv_upper[1], hsv_upper[2]));
                
                // OpenCV conversion reference
                ui.separator();
                ui.strong("📖 Color Preset Reference (from main.py):");
                ui.label("🟡 Yellow: OpenCV [30, 125, 150] - [30, 255, 255]");
                ui.label("🟨 Yellow 2: OpenCV [30, 170, 254] - [30, 230, 255]");
                ui.label("🟣 Purple: OpenCV [144, 72, 150] - [152, 255, 255]");
                ui.label("🔮 Anti Astra: OpenCV [140, 86, 172] - [150, 255, 255]");
                ui.label("🔴 Red: OpenCV [0, 170, 150] - [5, 255, 255]");
                
                ui.separator();
                ui.strong("🔄 HSV Conversion Formula:");
                ui.label("OpenCV HSV → Rust HSV conversion:");
                ui.label("• H: 0-179 → 0.0-1.0 (divide by 179)");
                ui.label("• S: 0-255 → 0.0-1.0 (divide by 255)");
                ui.label("• V: 0-255 → 0.0-1.0 (divide by 255)");
                
                ui.separator();
                ui.strong("💡 HSV Color Space Guide:");
                ui.label("• H (Hue): Color type (0.0=Red, 0.33=Green, 0.67=Blue)");
                ui.label("• S (Saturation): Color intensity (0.0=Gray, 1.0=Vivid)");
                ui.label("• V (Value): Brightness (0.0=Black, 1.0=Bright)");
                
                ui.separator();
                ui.checkbox(&mut self.show_filtered, "🎨 Show Color Filter Preview");
                
                drop(config); // Release the lock before the button
                if ui.button("💾 Save All Color Settings").clicked() {
                    let _ = self.config.lock().save_to_file();

                }
            });
            
            ui.separator();
            
            // Preview section with color detection visualization
            if self.show_preview && self.capture_enabled {
                let texture_to_show = if self.show_filtered {
                    &self.filtered_texture
                } else {
                    &self.texture
                };
                
                if let Some(texture) = texture_to_show {
                    let scale_factor = 3.0;
                    let display_size = egui::Vec2::new(
                        self.current_capture_size as f32 * scale_factor,
                        self.current_capture_size as f32 * scale_factor,
                    );
                    
                    // Add a frame around the preview to show it's active
                    ui.group(|ui| {
                        ui.label(format!("📺 Live Preview - {}x{} → {}x{} (3x scale)", 
                            self.current_capture_size, self.current_capture_size,
                            self.current_capture_size * 3, self.current_capture_size * 3));
                        
                        ui.horizontal(|ui| {
                            ui.add(egui::Image::new(texture).fit_to_exact_size(display_size));
                            
                            // Add color detection status panel
                            ui.vertical(|ui| {
                                ui.strong("🎯 Detection Status:");
                                
                                let config = self.config.lock();
                                let (hsv_lower, hsv_upper) = config.get_current_hsv_range();
                                
                                ui.label(format!("Target: {}", config.color_preset.name()));
                                ui.separator();
                                ui.label("HSV Range:");
                                ui.label(format!("H: {:.3}-{:.3}", hsv_lower[0], hsv_upper[0]));
                                ui.label(format!("S: {:.3}-{:.3}", hsv_lower[1], hsv_upper[1]));
                                ui.label(format!("V: {:.3}-{:.3}", hsv_lower[2], hsv_upper[2]));
                                
                                ui.separator();
                                ui.label("📊 Detection Stats:");
                                ui.label(format!("Aim FOV: {}px", config.aim_fov));
                                ui.label(format!("Trigger FOV: {}px", config.trigger_fov));
                                
                                // Show filter toggle
                                ui.separator();
                                if ui.button(if self.show_filtered { 
                                    "👀 Show Original" 
                                } else { 
                                    "🎨 Show Filtered" 
                                }).clicked() {
                                    self.show_filtered = !self.show_filtered;
                                }
                                
                                ui.label(if self.show_filtered {
                                    "Showing: Filtered view\n(Detected colors bright,\nothers dimmed)"
                                } else {
                                    "Showing: Original capture\n(Raw screen data)"
                                });
                            });
                        });
                    });
                } else {
                    ui.group(|ui| {
                        ui.label("⏳ Loading preview...");
                        ui.spinner();
                    });
                }
            } else if !self.capture_enabled {
                ui.group(|ui| {
                    ui.colored_label(egui::Color32::RED, "📸 Capture is disabled");
                    ui.label("Enable capture to see preview and start detection");
                });
            } else {
                ui.group(|ui| {
                    ui.colored_label(egui::Color32::YELLOW, "📺 Preview hidden for maximum performance");
                    ui.label("🎯 Target detection still running in background");
                    ui.label("Press F9 to show preview");
                });
            }
            
            ui.separator();
            ui.horizontal(|ui| {
                ui.colored_label(egui::Color32::YELLOW, "🎮 F9: Toggle Preview | F10: Toggle Capture | F11: Toggle Detection");
            });
        });

        if self.show_preview && self.capture_enabled {
            ctx.request_repaint_after(Duration::from_millis(16));
        } else {
            ctx.request_repaint_after(Duration::from_millis(100));
        }
    }
}

fn capture_loop_optimized(
    frame_data: Arc<Mutex<Option<FrameData>>>,
    config: Arc<Mutex<CaptureConfig>>,
    is_running: Arc<AtomicBool>,
) {
    let mut current_capture_size = config.lock().aim_fov;
    let mut capturer = match DirectXCapture::new(current_capture_size) {
        Ok(c) => c,
        Err(_) => return,
    };

    let target_detector = match TargetDetector::new(config.clone()) {
        Ok(detector) => Some(detector),
        Err(_) => None,
    };

    let mut fps_counter = 0u32;
    let mut fps_timer = Instant::now();
    let mut current_fps = 0.0f32;
    let mut last_valid_pixels: Option<Vec<egui::Color32>> = None;

    while is_running.load(Ordering::Relaxed) {
        // Check for capture size changes
        let new_capture_size = config.lock().aim_fov;
        if new_capture_size != current_capture_size {
            current_capture_size = new_capture_size;
            capturer = match DirectXCapture::new(current_capture_size) {
                Ok(c) => c,
                Err(_) => continue,
            };
        }

        match capturer.capture_directx() {
            Ok(Some(pixels)) => {
                fps_counter += 1;
                if fps_timer.elapsed() >= Duration::from_secs(1) {
                    current_fps = fps_counter as f32;
                    fps_counter = 0;
                    fps_timer = Instant::now();
                }

                last_valid_pixels = Some(pixels.clone());                // Process target detection and send UDP messages
                if let Some(ref detector) = target_detector {
                    let mut aim_target_found = false;
                    let mut trigger_target_found = false;
                    
                    // Get current HSV range from config
                    let (hsv_lower, hsv_upper) = config.lock().get_current_hsv_range();
                    
                    // Aim target detection
                    if let Some(target) = detector.detect_target_in_pixels(&pixels, current_capture_size, &hsv_lower, &hsv_upper) {
                        let _ = detector.send_target_data(&target, current_capture_size, "aim");
                        aim_target_found = true;
                    }
                    
                    // Trigger target detection (smaller FOV)
                    let trigger_fov = config.lock().trigger_fov.min(current_capture_size);
                    let start_offset = (current_capture_size - trigger_fov) / 2;
                    
                    let mut trigger_pixels = Vec::new();
                    for y in start_offset..(start_offset + trigger_fov) {
                        for x in start_offset..(start_offset + trigger_fov) {
                            let idx = (y * current_capture_size + x) as usize;
                            if idx < pixels.len() {
                                trigger_pixels.push(pixels[idx]);
                            }
                        }
                    }
                    
                    if let Some(target) = detector.detect_target_in_pixels(&trigger_pixels, trigger_fov, &hsv_lower, &hsv_upper) {
                        let _ = detector.send_target_data(&target, trigger_fov, "trigger");
                        trigger_target_found = true;
                    }
                      // Send clear message if no targets were found
                    if !aim_target_found && !trigger_target_found {
                        let _ = detector.send_clear_message();
                    }
                }

                // Apply color filtering in parallel
                let (hsv_lower, hsv_upper) = config.lock().get_current_hsv_range();
                let filtered_pixels = apply_color_filter(&pixels, &hsv_lower, &hsv_upper);

                let data = FrameData {
                    pixels,
                    filtered_pixels,
                    fps: current_fps,
                };

                *frame_data.lock() = Some(data);
            }
            Ok(None) => {
                // No new frame - continue without updating
            }
            Err(_) => {
                // Error - use backup if available
                if let Some(ref backup_pixels) = last_valid_pixels {
                    let (hsv_lower, hsv_upper) = config.lock().get_current_hsv_range();
                    let filtered_pixels = apply_color_filter(backup_pixels, &hsv_lower, &hsv_upper);
                    let data = FrameData {
                        pixels: backup_pixels.clone(),
                        filtered_pixels,
                        fps: current_fps,
                    };
                    *frame_data.lock() = Some(data);
                }
                
                thread::sleep(Duration::from_millis(1));
                continue;
            }
        }

        // Minimal sleep for high performance
        thread::sleep(Duration::from_micros(100));
    }
}



pub fn main() -> eframe::Result<()> {
    unsafe {
        let process = GetCurrentProcess();
        let _ = SetPriorityClass(process, HIGH_PRIORITY_CLASS);
    }

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([500.0, 600.0])
            .with_title("📸 Screen Capture Engine")
            .with_resizable(true),
        vsync: false,
        ..Default::default()
    };
    
    eframe::run_native(
        "Screen Capture Engine",
        options,
        Box::new(|cc| Ok(Box::new(ScreenCaptureApp::new(cc)))),
    )
}
