use eframe::egui;
use egui::TextureHandle;
use parking_lot::Mutex;
use std::sync::{Arc, atomic::{AtomicBool, Ordering}};
use std::thread;
use std::time::{Duration, Instant};
use std::net::UdpSocket;
use windows::core::*;
use windows::Win32::Foundation::*;
use windows::Win32::Graphics::Direct3D::*;
use windows::Win32::Graphics::Direct3D11::*;
use windows::Win32::Graphics::Dxgi::*;
use windows::Win32::Graphics::Dxgi::Common::*;

use windows::Win32::UI::WindowsAndMessaging::*;
use serde::{Deserialize, Serialize};
use serde_json;
use anyhow::{Result, Context};
use rayon::prelude::*;
use aes::Aes256;
use aes::cipher::{BlockEncrypt, BlockDecrypt, KeyInit, generic_array::GenericArray};
use rand::RngCore;
use base64::{Engine as _, engine::general_purpose};
// YOLO integration temporarily disabled due to API changes
// use ort::{...};
// use ndarray::Array4; // Temporarily unused
use toml;

mod network_discovery;
use network_discovery::NetworkDiscovery;

// Default encryption key (must match action.rs)
const DEFAULT_ENCRYPTION_KEY: &str = "SilenceCapture2024KeyForSecureUDP!!";

// AI Detection types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum DetectionMode {
    ColorBased,
    AIBased,
    Hybrid, // Both color and AI
}

impl Default for DetectionMode {
    fn default() -> Self {
        DetectionMode::ColorBased
    }
}

// Color presets (same as existing capture.rs)
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ColorPreset {
    Yellow,
    Yellow2,
    Purple,
    AntiAstra,
    Red,
    Custom,
}

impl Default for ColorPreset {
    fn default() -> Self {
        ColorPreset::Purple
    }
}

impl ColorPreset {
    pub fn to_hsv_range(&self) -> ([f32; 3], [f32; 3]) {
        match self {
            ColorPreset::Yellow => (
                [0.15000000596046448, 0.41499999165534973, 0.7300000190734863],
                [0.17000000178813934, 1.0, 1.0]
            ),
            ColorPreset::Yellow2 => (
                Self::opencv_to_rust_hsv([30.0, 170.0, 254.0]),
                Self::opencv_to_rust_hsv([30.0, 230.0, 255.0])
            ),
            ColorPreset::Purple => (
                Self::opencv_to_rust_hsv([144.0, 72.0, 150.0]),
                Self::opencv_to_rust_hsv([152.0, 255.0, 255.0])
            ),
            ColorPreset::AntiAstra => (
                Self::opencv_to_rust_hsv([140.0, 86.0, 172.0]),
                Self::opencv_to_rust_hsv([150.0, 255.0, 255.0])
            ),
            ColorPreset::Red => (
                Self::opencv_to_rust_hsv([0.0, 170.0, 150.0]),
                Self::opencv_to_rust_hsv([5.0, 255.0, 255.0])
            ),
            ColorPreset::Custom => (
                [0.0, 0.0, 0.0],
                [1.0, 1.0, 1.0]
            ),
        }
    }

    fn opencv_to_rust_hsv(opencv_hsv: [f32; 3]) -> [f32; 3] {
        [
            opencv_hsv[0] / 180.0,
            opencv_hsv[1] / 255.0,
            opencv_hsv[2] / 255.0,
        ]
    }
}

// AI Detection result from YOLO
#[derive(Debug, Clone)]
pub struct AIDetection {
    pub center_x: f32,
    pub center_y: f32,
    pub confidence: f32,
    pub bbox: [f32; 4], // xmin, ymin, xmax, ymax
    pub class_id: i32,
}

// Encryption utilities (same as existing capture.rs)
struct CryptoUtils {
    cipher: Option<Aes256>,
}

impl CryptoUtils {
    fn new() -> Self {
        Self { cipher: None }
    }

    fn pad_data(data: &[u8]) -> Vec<u8> {
        let mut padded = data.to_vec();
        let padding_len = 16 - (data.len() % 16);
        for _ in 0..padding_len {
            padded.push(padding_len as u8);
        }
        padded
    }

    fn unpad_data(data: &[u8]) -> Result<Vec<u8>> {
        if data.is_empty() {
            return Err(anyhow::anyhow!("Empty data"));
        }
        
        let padding_len = data[data.len() - 1] as usize;
        if padding_len > 16 || padding_len > data.len() {
            return Err(anyhow::anyhow!("Invalid padding"));
        }
        
        Ok(data[..data.len() - padding_len].to_vec())
    }
    
    fn encrypt(data: &str, key: &str) -> Result<String> {
        let mut key_bytes = [0u8; 32];
        let key_data = key.as_bytes();
        let copy_len = key_data.len().min(32);
        key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
        
        let cipher = Aes256::new(GenericArray::from_slice(&key_bytes));
        let padded_data = Self::pad_data(data.as_bytes());
        
        let mut encrypted = Vec::new();
        
        for chunk in padded_data.chunks(16) {
            let mut block = GenericArray::clone_from_slice(chunk);
            cipher.encrypt_block(&mut block);
            encrypted.extend_from_slice(&block);
        }
        
        let mut iv = [0u8; 16];
        rand::thread_rng().fill_bytes(&mut iv);
        
        let mut result = iv.to_vec();
        result.extend(encrypted);
        
        Ok(general_purpose::STANDARD.encode(&result))
    }
    
    fn decrypt(encrypted_data: &str, key: &str) -> Result<String> {
        let data = general_purpose::STANDARD.decode(encrypted_data)
            .context("Failed to decode base64")?;
        
        if data.len() < 32 {
            return Err(anyhow::anyhow!("Encrypted data too short"));
        }

        let mut key_bytes = [0u8; 32];
        let key_data = key.as_bytes();
        let copy_len = key_data.len().min(32);
        key_bytes[..copy_len].copy_from_slice(&key_data[..copy_len]);
        
        let cipher = Aes256::new(GenericArray::from_slice(&key_bytes));
        let encrypted_data = &data[16..];
        
        let mut decrypted = Vec::new();
        for chunk in encrypted_data.chunks(16) {
            let mut block = GenericArray::clone_from_slice(chunk);
            cipher.decrypt_block(&mut block);
            decrypted.extend_from_slice(&block);
        }
        
        let unpadded = Self::unpad_data(&decrypted)?;
        String::from_utf8(unpadded).context("Failed to convert decrypted data to string")
    }
}

// UDP Message structure for communication
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AITargetMessage {
    pub center_x: f32,
    pub center_y: f32,
    pub confidence: f32,
    pub capture_size: u32,
    pub message_type: String, // "aim", "trigger", "silent", or "clear"
    pub timestamp: u64,
    pub detection_mode: String, // "color", "ai", or "hybrid"
    pub bbox: Option<[f32; 4]>, // For AI detections
    pub class_id: Option<i32>, // For AI detections
}

// Configuration for AI Capture
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AICaptureConfig {
    // Existing capture settings
    pub aim_fov: u32,
    pub trigger_fov: u32,
    pub udp_port: u16,
    pub target_ip: String,
    pub enable_encryption: bool,
    pub encryption_key: String,
    
    // Color detection settings
    pub color_preset: ColorPreset,
    pub custom_hsv_lower: [f32; 3],
    pub custom_hsv_upper: [f32; 3],
    
    // AI detection settings
    pub detection_mode: DetectionMode,
    pub enable_ai_detection: bool,
    pub ai_model_path: String,
    pub ai_confidence_threshold: f32,
    pub ai_imgsz: u32,
    pub ai_max_detections: u32,
    pub ai_target_class: i32, // Class ID to target (e.g., 1 for person)
    
    // Performance settings
    pub ai_inference_interval_ms: u64, // How often to run AI inference
    pub enable_gpu: bool,
    pub gpu_device_id: i32,
}

impl Default for AICaptureConfig {
    fn default() -> Self {
        Self {
            aim_fov: 65,
            trigger_fov: 8,
            udp_port: 12346,
            target_ip: "127.0.0.1:12345".to_string(),
            enable_encryption: true,
            encryption_key: DEFAULT_ENCRYPTION_KEY.to_string(),
            color_preset: ColorPreset::Purple,
            custom_hsv_lower: [0.8, 0.282, 0.588],
            custom_hsv_upper: [0.844, 1.0, 1.0],
            detection_mode: DetectionMode::ColorBased,
            enable_ai_detection: false,
            ai_model_path: "custom_best.onnx".to_string(),
            ai_confidence_threshold: 0.4,
            ai_imgsz: 736,
            ai_max_detections: 300,
            ai_target_class: 1,
            ai_inference_interval_ms: 16, // ~60 FPS
            enable_gpu: true,
            gpu_device_id: 0,
        }
    }
}

impl AICaptureConfig {
    pub fn load_from_file() -> Self {
        match std::fs::read_to_string("ai_capture_config.toml") {
            Ok(content) => {
                match toml::from_str::<AICaptureConfig>(&content) {
                    Ok(config) => config,
                    Err(_) => Self::default()
                }
            }
            Err(_) => Self::default()
        }
    }

    pub fn save_to_file(&self) -> Result<()> {
        let content = toml::to_string_pretty(self)?;
        std::fs::write("ai_capture_config.toml", content)?;
        Ok(())
    }
    
    pub fn get_current_hsv_range(&self) -> ([f32; 3], [f32; 3]) {
        match self.color_preset {
            ColorPreset::Custom => (self.custom_hsv_lower, self.custom_hsv_upper),
            _ => self.color_preset.to_hsv_range(),
        }
    }
}

// Target detection result
#[derive(Debug, Clone)]
pub struct TargetInfo {
    pub center_x: f32,
    pub center_y: f32,
    pub confidence: f32,
    pub detection_source: String, // "color", "ai", or "hybrid"
    pub bbox: Option<[f32; 4]>,
    pub class_id: Option<i32>,
}

// Frame data structure
#[derive(Debug, Clone)]
pub struct FrameData {
    pub pixels: Vec<egui::Color32>,
    pub filtered_pixels: Vec<egui::Color32>,
    pub fps: f32,
    pub ai_detections: Vec<AIDetection>,
}

// HSV conversion functions (same as existing capture.rs)
fn rgb_to_hsv(r: f32, g: f32, b: f32) -> [f32; 3] {
    let max = r.max(g.max(b));
    let min = r.min(g.min(b));
    let delta = max - min;

    let h = if delta == 0.0 {
        0.0
    } else if max == r {
        60.0 * (((g - b) / delta) % 6.0)
    } else if max == g {
        60.0 * (((b - r) / delta) + 2.0)
    } else {
        60.0 * (((r - g) / delta) + 4.0)
    };

    let s = if max == 0.0 { 0.0 } else { delta / max };
    let v = max;

    [h / 360.0, s, v]
}

fn is_target_color(hsv: [f32; 3], hsv_lower: &[f32; 3], hsv_upper: &[f32; 3]) -> bool {
    hsv[0] >= hsv_lower[0] && hsv[0] <= hsv_upper[0] &&
    hsv[1] >= hsv_lower[1] && hsv[1] <= hsv_upper[1] &&
    hsv[2] >= hsv_lower[2] && hsv[2] <= hsv_upper[2]
}

// Apply color filtering using parallel processing
fn apply_color_filter(pixels: &[egui::Color32], hsv_lower: &[f32; 3], hsv_upper: &[f32; 3]) -> Vec<egui::Color32> {
    pixels.par_iter().map(|&pixel| {
        let r = pixel.r() as f32 / 255.0;
        let g = pixel.g() as f32 / 255.0;
        let b = pixel.b() as f32 / 255.0;

        let hsv = rgb_to_hsv(r, g, b);

        if is_target_color(hsv, hsv_lower, hsv_upper) {
            pixel
        } else {
            egui::Color32::from_rgba_premultiplied(
                (pixel.r() as f32 * 0.1) as u8,
                (pixel.g() as f32 * 0.1) as u8,
                (pixel.b() as f32 * 0.1) as u8,
                pixel.a(),
            )
        }
    }).collect()
}

// DirectX Capture (same as existing capture.rs)
struct DirectXCapture {
    context: ID3D11DeviceContext,
    duplication: IDXGIOutputDuplication,
    staging_texture: ID3D11Texture2D,
    center_x: u32,
    center_y: u32,
    capture_size: u32,
}

impl DirectXCapture {
    fn new(capture_size: u32) -> Result<Self> {
        unsafe {
            let factory: IDXGIFactory1 = CreateDXGIFactory1()?;
            let adapter = factory.EnumAdapters1(0)?;
            let output = adapter.EnumOutputs(0)?;
            let output1: IDXGIOutput1 = output.cast()?;

            let mut device = None;
            let mut context = None;
            let feature_levels = [D3D_FEATURE_LEVEL_11_0];

            D3D11CreateDevice(
                &adapter,
                D3D_DRIVER_TYPE_UNKNOWN,
                HMODULE::default(),
                D3D11_CREATE_DEVICE_FLAG::default(),
                Some(&feature_levels),
                D3D11_SDK_VERSION,
                Some(&mut device),
                None,
                Some(&mut context),
            )?;

            let device = device.unwrap();
            let context = context.unwrap();

            let duplication = output1.DuplicateOutput(&device)?;

            let desc = D3D11_TEXTURE2D_DESC {
                Width: capture_size,
                Height: capture_size,
                MipLevels: 1,
                ArraySize: 1,
                Format: DXGI_FORMAT_B8G8R8A8_UNORM,
                SampleDesc: DXGI_SAMPLE_DESC {
                    Count: 1,
                    Quality: 0,
                },
                Usage: D3D11_USAGE_STAGING,
                BindFlags: D3D11_BIND_FLAG::default().0 as u32,
                CPUAccessFlags: D3D11_CPU_ACCESS_READ.0 as u32,
                MiscFlags: D3D11_RESOURCE_MISC_FLAG::default().0 as u32,
            };

            let mut staging_texture = None;
            device.CreateTexture2D(&desc, None, Some(&mut staging_texture))?;
            let staging_texture = staging_texture.unwrap();

            let mut desktop_rect = windows::Win32::Foundation::RECT::default();
            GetWindowRect(GetDesktopWindow(), &mut desktop_rect)?;
            let screen_width = desktop_rect.right - desktop_rect.left;
            let screen_height = desktop_rect.bottom - desktop_rect.top;

            let center_x = (screen_width / 2 - capture_size as i32 / 2) as u32;
            let center_y = (screen_height / 2 - capture_size as i32 / 2) as u32;

            Ok(Self {
                context,
                duplication,
                staging_texture,
                center_x,
                center_y,
                capture_size,
            })
        }
    }

    fn capture_directx(&mut self) -> Result<Option<Vec<egui::Color32>>> {
        unsafe {
            let mut frame_info = DXGI_OUTDUPL_FRAME_INFO::default();
            let mut desktop_resource = None;

            match self.duplication.AcquireNextFrame(0, &mut frame_info, &mut desktop_resource) {
                Ok(_) => {
                    if let Some(desktop_resource) = desktop_resource {
                        let desktop_texture: ID3D11Texture2D = desktop_resource.cast()?;

                        let src_box = D3D11_BOX {
                            left: self.center_x,
                            top: self.center_y,
                            front: 0,
                            right: self.center_x + self.capture_size,
                            bottom: self.center_y + self.capture_size,
                            back: 1,
                        };

                        self.context.CopySubresourceRegion(
                            &self.staging_texture,
                            0,
                            0,
                            0,
                            0,
                            &desktop_texture,
                            0,
                            Some(&src_box),
                        );

                        let mut mapped = D3D11_MAPPED_SUBRESOURCE::default();
                        self.context.Map(&self.staging_texture, 0, D3D11_MAP_READ, 0, Some(&mut mapped))?;

                        if !mapped.pData.is_null() {
                            let row_pitch = mapped.RowPitch as usize;
                            let data_ptr = mapped.pData as *const u8;
                            let mut pixels = Vec::with_capacity((self.capture_size * self.capture_size) as usize);

                            for y in 0..self.capture_size {
                                let row_start = (y as usize * row_pitch) as isize;
                                for x in 0..self.capture_size {
                                    let pixel_offset = row_start + (x as isize * 4);
                                    let pixel_ptr = data_ptr.offset(pixel_offset);

                                    let b = *pixel_ptr;
                                    let g = *pixel_ptr.offset(1);
                                    let r = *pixel_ptr.offset(2);
                                    let a = *pixel_ptr.offset(3);

                                    pixels.push(egui::Color32::from_rgba_premultiplied(r, g, b, a));
                                }
                            }

                            self.context.Unmap(&self.staging_texture, 0);
                            let _ = self.duplication.ReleaseFrame();

                            Ok(Some(pixels))
                        } else {
                            Err(windows::core::Error::from(E_FAIL).into())
                        }
                    } else {
                        Err(windows::core::Error::from(E_FAIL).into())
                    }
                }
                Err(e) => {
                    if e.code() == DXGI_ERROR_WAIT_TIMEOUT {
                        Ok(None)
                    } else {
                        Err(e.into())
                    }
                }
            }
        }
    }
}

// YOLO Model wrapper (placeholder implementation)
pub struct YOLOModel {
    model_path: String,
    input_shape: (usize, usize), // (width, height)
}

impl YOLOModel {
    pub fn new(model_path: &str, _enable_gpu: bool, _gpu_device_id: i32) -> Result<Self> {
        // TODO: Implement actual YOLO model loading
        // For now, just validate that the model file exists
        if !std::path::Path::new(model_path).exists() {
            return Err(anyhow::anyhow!("Model file not found: {}", model_path));
        }

        println!("📝 YOLO model placeholder created for: {}", model_path);
        println!("💡 To enable full AI detection, implement ONNX Runtime integration");

        Ok(Self {
            model_path: model_path.to_string(),
            input_shape: (640, 640), // Default YOLOv8 input size
        })
    }

    pub fn predict(&self, _image_data: &[egui::Color32], _image_size: u32, _confidence_threshold: f32, _max_detections: u32, _target_class: i32) -> Result<Vec<AIDetection>> {
        // TODO: Implement actual YOLO inference
        // For now, return empty detections

        // Uncomment this line to simulate detections for testing:
        // self.simulate_detections(image_size, confidence_threshold, max_detections)

        Ok(Vec::new())
    }

    #[allow(dead_code)]
    fn simulate_detections(&self, image_size: u32, confidence_threshold: f32, max_detections: u32) -> Result<Vec<AIDetection>> {
        // Simulate some detections for testing purposes
        let mut detections = Vec::new();

        if max_detections > 0 && confidence_threshold <= 0.8 {
            let center = image_size as f32 / 2.0;
            let size = 50.0;

            detections.push(AIDetection {
                center_x: center + (rand::random::<f32>() - 0.5) * 20.0,
                center_y: center + (rand::random::<f32>() - 0.5) * 20.0,
                confidence: 0.85,
                bbox: [center - size, center - size, center + size, center + size],
                class_id: 1, // Person class
            });
        }

        Ok(detections)
    }
}

// AI Target Detector with YOLO integration
pub struct AITargetDetector {
    udp_socket: UdpSocket,
    config: Arc<Mutex<AICaptureConfig>>,
    last_ai_inference: Instant,
    yolo_model: Option<YOLOModel>,
}

impl AITargetDetector {
    pub fn new(config: Arc<Mutex<AICaptureConfig>>) -> Result<Self> {
        let socket = UdpSocket::bind(format!("0.0.0.0:{}", config.lock().udp_port))?;
        socket.set_nonblocking(true)?;

        // Initialize YOLO model if AI detection is enabled
        let yolo_model = {
            let config_guard = config.lock();
            if config_guard.enable_ai_detection {
                match YOLOModel::new(
                    &config_guard.ai_model_path,
                    config_guard.enable_gpu,
                    config_guard.gpu_device_id,
                ) {
                    Ok(model) => {
                        println!("✅ YOLO model loaded successfully: {}", config_guard.ai_model_path);
                        Some(model)
                    }
                    Err(e) => {
                        println!("❌ Failed to load YOLO model: {}", e);
                        println!("💡 Make sure the model file exists at: {}", config_guard.ai_model_path);
                        None
                    }
                }
            } else {
                None
            }
        };

        Ok(Self {
            udp_socket: socket,
            config,
            last_ai_inference: Instant::now(),
            yolo_model,
        })
    }

    // Color-based target detection (same as existing capture.rs)
    pub fn detect_color_target_in_pixels(&self, pixels: &[egui::Color32], fov: u32, hsv_lower: [f32; 3], hsv_upper: [f32; 3]) -> Option<TargetInfo> {
        let mut total_weight = 0.0;
        let mut weighted_x = 0.0;
        let mut weighted_y = 0.0;
        let mut target_count = 0;

        for (i, &pixel) in pixels.iter().enumerate() {
            let x = (i % fov as usize) as f32;
            let y = (i / fov as usize) as f32;

            let r = pixel.r() as f32 / 255.0;
            let g = pixel.g() as f32 / 255.0;
            let b = pixel.b() as f32 / 255.0;

            let hsv = rgb_to_hsv(r, g, b);

            if is_target_color(hsv, &hsv_lower, &hsv_upper) {
                target_count += 1;
                let weight = (r + g + b) / 3.0;
                total_weight += weight;
                weighted_x += x * weight;
                weighted_y += y * weight;
            }
        }

        if target_count > 10 && total_weight > 0.0 {
            Some(TargetInfo {
                center_x: weighted_x / total_weight,
                center_y: weighted_y / total_weight,
                confidence: (target_count as f32 / pixels.len() as f32).min(1.0),
                detection_source: "color".to_string(),
                bbox: None,
                class_id: None,
            })
        } else {
            None
        }
    }

    // AI-based target detection using YOLO
    pub fn detect_ai_targets_in_pixels(&mut self, pixels: &[egui::Color32], fov: u32) -> Vec<AIDetection> {
        let config = self.config.lock();

        // Check if enough time has passed for next AI inference
        if self.last_ai_inference.elapsed().as_millis() < config.ai_inference_interval_ms as u128 {
            return Vec::new();
        }

        if !config.enable_ai_detection {
            return Vec::new();
        }

        // Check if YOLO model is loaded
        let yolo_model = match &self.yolo_model {
            Some(model) => model,
            None => {
                // Try to reload model if it wasn't loaded initially
                match YOLOModel::new(
                    &config.ai_model_path,
                    config.enable_gpu,
                    config.gpu_device_id,
                ) {
                    Ok(model) => {
                        println!("✅ YOLO model loaded successfully: {}", config.ai_model_path);
                        // Note: We can't assign to self.yolo_model here due to borrow checker
                        // This is a temporary model instance for this inference
                        drop(config);
                        self.last_ai_inference = Instant::now();
                        return model.predict(
                            pixels,
                            fov,
                            self.config.lock().ai_confidence_threshold,
                            self.config.lock().ai_max_detections,
                            self.config.lock().ai_target_class,
                        ).unwrap_or_default();
                    }
                    Err(e) => {
                        println!("❌ Failed to load YOLO model: {}", e);
                        return Vec::new();
                    }
                }
            }
        };

        // Run YOLO inference
        let detections = match yolo_model.predict(
            pixels,
            fov,
            config.ai_confidence_threshold,
            config.ai_max_detections,
            config.ai_target_class,
        ) {
            Ok(detections) => {
                if !detections.is_empty() {
                    println!("🎯 AI detected {} targets with confidence > {:.2}",
                        detections.len(), config.ai_confidence_threshold);
                }
                detections
            }
            Err(e) => {
                println!("❌ YOLO inference failed: {}", e);
                Vec::new()
            }
        };

        self.last_ai_inference = Instant::now();
        detections
    }

    // Reload YOLO model (useful when configuration changes)
    pub fn reload_yolo_model(&mut self) -> Result<()> {
        let config = self.config.lock();

        if config.enable_ai_detection {
            match YOLOModel::new(
                &config.ai_model_path,
                config.enable_gpu,
                config.gpu_device_id,
            ) {
                Ok(model) => {
                    println!("✅ YOLO model reloaded successfully: {}", config.ai_model_path);
                    self.yolo_model = Some(model);
                    Ok(())
                }
                Err(e) => {
                    println!("❌ Failed to reload YOLO model: {}", e);
                    self.yolo_model = None;
                    Err(e)
                }
            }
        } else {
            self.yolo_model = None;
            Ok(())
        }
    }

    // Check if YOLO model is loaded and ready
    pub fn is_yolo_model_loaded(&self) -> bool {
        self.yolo_model.is_some()
    }

    // Find closest target from AI detections
    pub fn find_closest_ai_target(&self, detections: &[AIDetection], fov: u32) -> Option<TargetInfo> {
        if detections.is_empty() {
            return None;
        }

        let fov_center = fov as f32 / 2.0;
        let mut closest_distance = f32::MAX;
        let mut closest_detection: Option<&AIDetection> = None;

        for detection in detections {
            let distance = ((detection.center_x - fov_center).powi(2) + (detection.center_y - fov_center).powi(2)).sqrt();
            if distance < closest_distance {
                closest_distance = distance;
                closest_detection = Some(detection);
            }
        }

        if let Some(detection) = closest_detection {
            Some(TargetInfo {
                center_x: detection.center_x,
                center_y: detection.center_y,
                confidence: detection.confidence,
                detection_source: "ai".to_string(),
                bbox: Some(detection.bbox),
                class_id: Some(detection.class_id),
            })
        } else {
            None
        }
    }

    // Hybrid detection combining color and AI
    pub fn detect_hybrid_target(&mut self, pixels: &[egui::Color32], fov: u32, hsv_lower: [f32; 3], hsv_upper: [f32; 3]) -> Option<TargetInfo> {
        let color_target = self.detect_color_target_in_pixels(pixels, fov, hsv_lower, hsv_upper);
        let ai_detections = self.detect_ai_targets_in_pixels(pixels, fov);
        let ai_target = self.find_closest_ai_target(&ai_detections, fov);

        match (color_target, ai_target) {
            (Some(color), Some(ai)) => {
                // Prefer AI detection if confidence is high enough
                if ai.confidence > 0.7 {
                    Some(TargetInfo {
                        detection_source: "hybrid".to_string(),
                        ..ai
                    })
                } else {
                    Some(TargetInfo {
                        detection_source: "hybrid".to_string(),
                        ..color
                    })
                }
            }
            (Some(color), None) => Some(TargetInfo {
                detection_source: "hybrid".to_string(),
                ..color
            }),
            (None, Some(ai)) => Some(TargetInfo {
                detection_source: "hybrid".to_string(),
                ..ai
            }),
            (None, None) => None,
        }
    }

    // Main detection method that chooses based on configuration
    pub fn detect_target(&mut self, pixels: &[egui::Color32], fov: u32) -> Option<TargetInfo> {
        let (detection_mode, hsv_lower, hsv_upper) = {
            let config = self.config.lock();
            (config.detection_mode.clone(), config.get_current_hsv_range().0, config.get_current_hsv_range().1)
        };

        match detection_mode {
            DetectionMode::ColorBased => self.detect_color_target_in_pixels(pixels, fov, hsv_lower, hsv_upper),
            DetectionMode::AIBased => {
                let ai_detections = self.detect_ai_targets_in_pixels(pixels, fov);
                self.find_closest_ai_target(&ai_detections, fov)
            }
            DetectionMode::Hybrid => self.detect_hybrid_target(pixels, fov, hsv_lower, hsv_upper),
        }
    }

    // Send target data via UDP
    pub fn send_target_data(&self, target: &TargetInfo, capture_size: u32, message_type: &str) -> Result<()> {
        let message = AITargetMessage {
            center_x: target.center_x,
            center_y: target.center_y,
            confidence: target.confidence,
            capture_size,
            message_type: message_type.to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            detection_mode: target.detection_source.clone(),
            bbox: target.bbox,
            class_id: target.class_id,
        };

        let serialized = serde_json::to_string(&message)?;
        let config = self.config.lock();

        // Encrypt the message if encryption is enabled
        let data_to_send = if config.enable_encryption {
            match CryptoUtils::encrypt(&serialized, &config.encryption_key) {
                Ok(encrypted) => encrypted.into_bytes(),
                Err(e) => return Err(e),
            }
        } else {
            serialized.into_bytes()
        };

        self.udp_socket.send_to(&data_to_send, &config.target_ip)?;
        Ok(())
    }

    // Send clear message when no targets found
    pub fn send_clear_message(&self) -> Result<()> {
        let message = AITargetMessage {
            center_x: 0.0,
            center_y: 0.0,
            confidence: 0.0,
            capture_size: 0,
            message_type: "clear".to_string(),
            timestamp: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap()
                .as_millis() as u64,
            detection_mode: "none".to_string(),
            bbox: None,
            class_id: None,
        };

        let serialized = serde_json::to_string(&message)?;
        let config = self.config.lock();

        let data_to_send = if config.enable_encryption {
            match CryptoUtils::encrypt(&serialized, &config.encryption_key) {
                Ok(encrypted) => encrypted.into_bytes(),
                Err(e) => return Err(e),
            }
        } else {
            serialized.into_bytes()
        };

        self.udp_socket.send_to(&data_to_send, &config.target_ip)?;
        Ok(())
    }

    // Receive messages (for testing/debugging)
    pub fn try_receive_message(&mut self) -> Result<Option<AITargetMessage>> {
        let mut buf = [0u8; 1024];
        match self.udp_socket.recv_from(&mut buf) {
            Ok((size, _addr)) => {
                let data = &buf[..size];
                let config = self.config.lock();

                let message_str = if config.enable_encryption {
                    let encrypted_str = String::from_utf8_lossy(data);
                    CryptoUtils::decrypt(&encrypted_str, &config.encryption_key)?
                } else {
                    String::from_utf8_lossy(data).to_string()
                };

                let message: AITargetMessage = serde_json::from_str(&message_str)?;
                Ok(Some(message))
            }
            Err(e) => {
                if e.kind() == std::io::ErrorKind::WouldBlock {
                    Ok(None) // No message available
                } else {
                    Err(anyhow::anyhow!("UDP receive error: {}", e))
                }
            }
        }
    }
}

// Main AI Screen Capture Application
pub struct AIScreenCaptureApp {
    texture: Option<TextureHandle>,
    filtered_texture: Option<TextureHandle>,
    frame_data: Arc<Mutex<Option<FrameData>>>,
    current_fps: f32,
    last_texture_update: Instant,
    texture_update_interval: Duration,
    show_filtered: bool,
    config: Arc<Mutex<AICaptureConfig>>,
    current_capture_size: u32,
    is_running: Arc<AtomicBool>,
    show_preview: bool,
    capture_enabled: bool,
    target_detection_enabled: bool,
    network_discovery: Option<NetworkDiscovery>,
    show_network_panel: bool,
    selected_device: Option<String>,
    ai_stats: AIStats,
}

#[derive(Debug, Default)]
struct AIStats {
    total_detections: u32,
    ai_detections: u32,
    color_detections: u32,
    hybrid_detections: u32,
    avg_confidence: f32,
    last_detection_time: Option<Instant>,
}

impl Default for AIScreenCaptureApp {
    fn default() -> Self {
        Self::new()
    }
}

impl AIScreenCaptureApp {
    pub fn new() -> Self {
        let config = Arc::new(Mutex::new(AICaptureConfig::load_from_file()));
        let current_capture_size = config.lock().aim_fov;

        Self {
            texture: None,
            filtered_texture: None,
            frame_data: Arc::new(Mutex::new(None)),
            current_fps: 0.0,
            last_texture_update: Instant::now(),
            texture_update_interval: Duration::from_millis(16), // ~60 FPS
            show_filtered: false,
            config,
            current_capture_size,
            is_running: Arc::new(AtomicBool::new(false)),
            show_preview: true,
            capture_enabled: false,
            target_detection_enabled: false,
            network_discovery: None,
            show_network_panel: false,
            selected_device: None,
            ai_stats: AIStats::default(),
        }
    }

    pub fn start_capture(&mut self) {
        if !self.capture_enabled {
            self.capture_enabled = true;
            self.is_running.store(true, Ordering::Relaxed);

            let frame_data = self.frame_data.clone();
            let config = self.config.clone();
            let is_running = self.is_running.clone();

            thread::spawn(move || {
                ai_capture_loop_optimized(frame_data, config, is_running);
            });
        }
    }

    pub fn stop_capture(&mut self) {
        self.capture_enabled = false;
        self.is_running.store(false, Ordering::Relaxed);
    }

    pub fn toggle_target_detection(&mut self) {
        self.target_detection_enabled = !self.target_detection_enabled;
    }
}

impl eframe::App for AIScreenCaptureApp {
    fn update(&mut self, ctx: &egui::Context, _frame: &mut eframe::Frame) {
        // Update FPS and texture data
        if let Some(data) = self.frame_data.lock().take() {
            self.current_fps = data.fps;

            // Update AI statistics
            if !data.ai_detections.is_empty() {
                self.ai_stats.total_detections += data.ai_detections.len() as u32;
                self.ai_stats.ai_detections += data.ai_detections.len() as u32;
                self.ai_stats.last_detection_time = Some(Instant::now());

                let avg_conf: f32 = data.ai_detections.iter().map(|d| d.confidence).sum::<f32>() / data.ai_detections.len() as f32;
                self.ai_stats.avg_confidence = (self.ai_stats.avg_confidence + avg_conf) / 2.0;
            }

            // Update textures at controlled interval
            if self.last_texture_update.elapsed() >= self.texture_update_interval {
                if self.show_preview {
                    let size = [self.current_capture_size as usize, self.current_capture_size as usize];

                    // Update main texture
                    let color_image = egui::ColorImage {
                        size,
                        pixels: data.pixels,
                    };

                    self.texture = Some(ctx.load_texture("capture", color_image, egui::TextureOptions::default()));

                    // Update filtered texture
                    if self.show_filtered {
                        let filtered_image = egui::ColorImage {
                            size,
                            pixels: data.filtered_pixels,
                        };

                        self.filtered_texture = Some(ctx.load_texture("filtered", filtered_image, egui::TextureOptions::default()));
                    }
                }

                self.last_texture_update = Instant::now();
            }
        }

        // Main UI
        egui::CentralPanel::default().show(ctx, |ui| {
            ui.heading("🎯 AI Screen Capture");

            ui.horizontal(|ui| {
                ui.label(format!("FPS: {:.1}", self.current_fps));
                ui.separator();
                ui.label(format!("Capture Size: {}x{}", self.current_capture_size, self.current_capture_size));
                ui.separator();

                if ui.button(if self.capture_enabled { "⏹ Stop" } else { "▶ Start" }).clicked() {
                    if self.capture_enabled {
                        self.stop_capture();
                    } else {
                        self.start_capture();
                    }
                }

                ui.separator();
                ui.checkbox(&mut self.target_detection_enabled, "🎯 Target Detection");
                ui.checkbox(&mut self.show_preview, "👁 Show Preview");
                ui.checkbox(&mut self.show_filtered, "🔍 Show Filtered");
            });

            ui.separator();

            // AI Configuration Settings
            ui.collapsing("🤖 AI Detection Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("Detection Mode:");
                    egui::ComboBox::from_label("")
                        .selected_text(format!("{:?}", config.detection_mode))
                        .show_ui(ui, |ui| {
                            ui.selectable_value(&mut config.detection_mode, DetectionMode::ColorBased, "Color Based");
                            ui.selectable_value(&mut config.detection_mode, DetectionMode::AIBased, "AI Based");
                            ui.selectable_value(&mut config.detection_mode, DetectionMode::Hybrid, "Hybrid (Color + AI)");
                        });
                });

                ui.separator();
                ui.checkbox(&mut config.enable_ai_detection, "🧠 Enable AI Detection");

                // Show model status
                if config.enable_ai_detection {
                    ui.horizontal(|ui| {
                        ui.label("Model Status:");
                        // Note: We can't easily check model status from UI thread without more complex state management
                        // For now, just show if AI detection is enabled
                        ui.colored_label(egui::Color32::GREEN, "✅ Enabled");

                        if ui.button("🔄 Reload Model").clicked() {
                            // This would need to be handled by the capture loop
                            println!("Model reload requested");
                        }
                    });
                }

                if config.enable_ai_detection {
                    ui.horizontal(|ui| {
                        ui.label("AI Model Path:");
                        ui.text_edit_singleline(&mut config.ai_model_path);
                    });

                    ui.label("💡 Supported formats: .onnx (recommended), .pt, .engine");
                    ui.label(format!("📁 Your models: custom_best.onnx, custom_best.pt, custom_best.engine"));

                    ui.horizontal(|ui| {
                        ui.label("Confidence Threshold:");
                        ui.add(egui::Slider::new(&mut config.ai_confidence_threshold, 0.1..=1.0).step_by(0.05));
                    });

                    ui.horizontal(|ui| {
                        ui.label("Image Size:");
                        ui.add(egui::DragValue::new(&mut config.ai_imgsz).range(320..=1280).speed(16));
                    });

                    ui.horizontal(|ui| {
                        ui.label("Max Detections:");
                        ui.add(egui::DragValue::new(&mut config.ai_max_detections).range(1..=500));
                    });

                    ui.horizontal(|ui| {
                        ui.label("Target Class ID:");
                        ui.add(egui::DragValue::new(&mut config.ai_target_class).range(0..=80));
                    });

                    ui.horizontal(|ui| {
                        ui.label("Inference Interval (ms):");
                        ui.add(egui::DragValue::new(&mut config.ai_inference_interval_ms).range(1..=100));
                    });

                    ui.horizontal(|ui| {
                        ui.checkbox(&mut config.enable_gpu, "� Enable GPU");
                        if config.enable_gpu {
                            ui.label("GPU Device ID:");
                            ui.add(egui::DragValue::new(&mut config.gpu_device_id).range(0..=7));
                        }
                    });
                }
            });

            // Color Detection Settings
            ui.collapsing("🎨 Color Detection Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("Color Preset:");
                    egui::ComboBox::from_label("")
                        .selected_text(format!("{:?}", config.color_preset))
                        .show_ui(ui, |ui| {
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Yellow, "Yellow");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Yellow2, "Yellow2");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Purple, "Purple");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::AntiAstra, "Anti-Astra");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Red, "Red");
                            ui.selectable_value(&mut config.color_preset, ColorPreset::Custom, "Custom");
                        });
                });

                if config.color_preset == ColorPreset::Custom {
                    ui.label("Custom HSV Lower:");
                    ui.horizontal(|ui| {
                        ui.label("H:"); ui.add(egui::Slider::new(&mut config.custom_hsv_lower[0], 0.0..=1.0));
                        ui.label("S:"); ui.add(egui::Slider::new(&mut config.custom_hsv_lower[1], 0.0..=1.0));
                        ui.label("V:"); ui.add(egui::Slider::new(&mut config.custom_hsv_lower[2], 0.0..=1.0));
                    });

                    ui.label("Custom HSV Upper:");
                    ui.horizontal(|ui| {
                        ui.label("H:"); ui.add(egui::Slider::new(&mut config.custom_hsv_upper[0], 0.0..=1.0));
                        ui.label("S:"); ui.add(egui::Slider::new(&mut config.custom_hsv_upper[1], 0.0..=1.0));
                        ui.label("V:"); ui.add(egui::Slider::new(&mut config.custom_hsv_upper[2], 0.0..=1.0));
                    });
                }
            });

            // Capture Settings
            ui.collapsing("⚙️ Capture Settings", |ui| {
                let mut config = self.config.lock();

                ui.horizontal(|ui| {
                    ui.label("Aim FOV:");
                    ui.add(egui::Slider::new(&mut config.aim_fov, 10..=200).suffix("px"));
                });

                ui.horizontal(|ui| {
                    ui.label("Trigger FOV:");
                    ui.add(egui::Slider::new(&mut config.trigger_fov, 5..=50).suffix("px"));
                });

                ui.horizontal(|ui| {
                    ui.label("UDP Port:");
                    ui.add(egui::DragValue::new(&mut config.udp_port).range(1024..=65535));
                });

                ui.horizontal(|ui| {
                    ui.label("Target IP:");
                    ui.text_edit_singleline(&mut config.target_ip);
                });

                ui.separator();
                ui.checkbox(&mut config.enable_encryption, "🔒 Enable UDP Encryption");

                if ui.button("💾 Save Config").clicked() {
                    let _ = config.save_to_file();
                }
            });

            // AI Statistics
            ui.collapsing("�📊 AI Detection Statistics", |ui| {
                ui.horizontal(|ui| {
                    ui.label(format!("Total Detections: {}", self.ai_stats.total_detections));
                    ui.separator();
                    ui.label(format!("AI Detections: {}", self.ai_stats.ai_detections));
                    ui.separator();
                    ui.label(format!("Avg Confidence: {:.2}", self.ai_stats.avg_confidence));
                });

                if let Some(last_time) = self.ai_stats.last_detection_time {
                    ui.label(format!("Last Detection: {:.1}s ago", last_time.elapsed().as_secs_f32()));
                }

                if ui.button("🔄 Reset Statistics").clicked() {
                    self.ai_stats = AIStats::default();
                }
            });

            // Network Discovery Panel
            if self.show_network_panel {
                ui.collapsing("🌐 Network Discovery", |ui| {
                    if let Some(ref mut discovery) = self.network_discovery {
                        let devices = discovery.get_devices();

                        ui.label(format!("Discovered {} devices:", devices.len()));

                        for device in devices {
                            ui.horizontal(|ui| {
                                ui.label(&device.ip);
                                ui.label(&device.name);

                                if ui.button("Connect").clicked() {
                                    self.selected_device = Some(device.ip.clone());
                                    let mut config = self.config.lock();
                                    config.target_ip = device.ip;
                                }
                            });
                        }

                        if ui.button("🔍 Refresh").clicked() {
                            let _ = discovery.start_discovery();
                        }
                    }
                });
            }
        });

        ctx.request_repaint();
    }
}

// Optimized AI capture loop
fn ai_capture_loop_optimized(
    frame_data: Arc<Mutex<Option<FrameData>>>,
    config: Arc<Mutex<AICaptureConfig>>,
    is_running: Arc<AtomicBool>,
) {
    let mut current_capture_size = config.lock().aim_fov;
    let mut capturer = match DirectXCapture::new(current_capture_size) {
        Ok(c) => c,
        Err(_) => return,
    };

    let mut target_detector = match AITargetDetector::new(config.clone()) {
        Ok(detector) => Some(detector),
        Err(_) => None,
    };

    let mut fps_counter = 0u32;
    let mut fps_timer = Instant::now();
    let mut current_fps = 0.0f32;
    let mut last_valid_pixels: Option<Vec<egui::Color32>> = None;

    while is_running.load(Ordering::Relaxed) {
        // Check for capture size changes
        let new_capture_size = config.lock().aim_fov;
        if new_capture_size != current_capture_size {
            current_capture_size = new_capture_size;
            capturer = match DirectXCapture::new(current_capture_size) {
                Ok(c) => c,
                Err(_) => continue,
            };
        }

        match capturer.capture_directx() {
            Ok(Some(pixels)) => {
                fps_counter += 1;
                if fps_timer.elapsed() >= Duration::from_secs(1) {
                    current_fps = fps_counter as f32;
                    fps_counter = 0;
                    fps_timer = Instant::now();
                }

                last_valid_pixels = Some(pixels.clone());

                let mut ai_detections = Vec::new();
                let mut aim_target_found = false;
                let mut trigger_target_found = false;

                // Process target detection and send UDP messages
                if let Some(ref mut detector) = target_detector {
                    // Aim target detection
                    if let Some(target) = detector.detect_target(&pixels, current_capture_size) {
                        let _ = detector.send_target_data(&target, current_capture_size, "aim");
                        aim_target_found = true;

                        // Store AI detection if it came from AI
                        if target.detection_source == "ai" || target.detection_source == "hybrid" {
                            if let (Some(bbox), Some(class_id)) = (target.bbox, target.class_id) {
                                ai_detections.push(AIDetection {
                                    center_x: target.center_x,
                                    center_y: target.center_y,
                                    confidence: target.confidence,
                                    bbox,
                                    class_id,
                                });
                            }
                        }
                    }

                    // Trigger target detection (smaller FOV)
                    let trigger_fov = config.lock().trigger_fov;
                    let trigger_pixels = if trigger_fov < current_capture_size {
                        // Extract center region for trigger detection
                        let start_offset = (current_capture_size - trigger_fov) / 2;
                        let mut trigger_pixels = Vec::new();

                        for y in start_offset..(start_offset + trigger_fov) {
                            for x in start_offset..(start_offset + trigger_fov) {
                                let idx = (y * current_capture_size + x) as usize;
                                if idx < pixels.len() {
                                    trigger_pixels.push(pixels[idx]);
                                }
                            }
                        }
                        trigger_pixels
                    } else {
                        pixels.clone()
                    };

                    if let Some(target) = detector.detect_target(&trigger_pixels, trigger_fov) {
                        let _ = detector.send_target_data(&target, trigger_fov, "trigger");
                        trigger_target_found = true;
                    }

                    // Send clear message if no targets were found
                    if !aim_target_found && !trigger_target_found {
                        let _ = detector.send_clear_message();
                    }
                }

                // Apply color filtering in parallel
                let (hsv_lower, hsv_upper) = config.lock().get_current_hsv_range();
                let filtered_pixels = apply_color_filter(&pixels, &hsv_lower, &hsv_upper);

                let data = FrameData {
                    pixels,
                    filtered_pixels,
                    fps: current_fps,
                    ai_detections,
                };

                *frame_data.lock() = Some(data);
            }
            Ok(None) => {
                // No new frame - continue without updating
            }
            Err(_) => {
                // Error - use backup if available
                if let Some(ref backup_pixels) = last_valid_pixels {
                    let (hsv_lower, hsv_upper) = config.lock().get_current_hsv_range();
                    let filtered_pixels = apply_color_filter(backup_pixels, &hsv_lower, &hsv_upper);
                    let data = FrameData {
                        pixels: backup_pixels.clone(),
                        filtered_pixels,
                        fps: current_fps,
                        ai_detections: Vec::new(),
                    };
                    *frame_data.lock() = Some(data);
                }

                thread::sleep(Duration::from_millis(1));
                continue;
            }
        }

        // Minimal sleep for high performance
        thread::sleep(Duration::from_micros(100));
    }
}

// Main function for running AI capture as standalone binary
fn main() -> eframe::Result<()> {
    // Set high priority for better performance
    unsafe {
        use windows::Win32::System::Threading::*;
        let process = GetCurrentProcess();
        let _ = SetPriorityClass(process, HIGH_PRIORITY_CLASS);
    }

    let options = eframe::NativeOptions {
        viewport: egui::ViewportBuilder::default()
            .with_inner_size([600.0, 800.0])
            .with_title("🤖 AI Screen Capture")
            .with_resizable(true),
        vsync: false,
        ..Default::default()
    };

    eframe::run_native(
        "AI Screen Capture",
        options,
        Box::new(|_cc| Ok(Box::new(AIScreenCaptureApp::new()))),
    )
}
