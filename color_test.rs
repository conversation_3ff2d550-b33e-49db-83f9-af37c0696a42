// Quick Color Test Utility
// This file demonstrates the color ranges for easy testing

use std::io::{self, Write};

fn main() {
    println!("🎨 Color Detection Test Utility");
    println!("================================");
    println!();
    
    // Print all color presets with their HSV ranges
    println!("Available Color Presets:");
    println!();
    
    // Yellow
    let yellow_lower = [30.0/179.0, 125.0/255.0, 150.0/255.0];
    let yellow_upper = [30.0/179.0, 255.0/255.0, 255.0/255.0];
    println!("🟡 Yellow:");
    println!("   OpenCV: [30, 125, 150] - [30, 255, 255]");
    println!("   Rust:   [{:.3}, {:.3}, {:.3}] - [{:.3}, {:.3}, {:.3}]", 
        yellow_lower[0], yellow_lower[1], yellow_lower[2],
        yellow_upper[0], yellow_upper[1], yellow_upper[2]);
    println!();
    
    // Yellow 2
    let yellow2_lower = [30.0/179.0, 170.0/255.0, 254.0/255.0];
    let yellow2_upper = [30.0/179.0, 230.0/255.0, 255.0/255.0];
    println!("🟨 Yellow 2:");
    println!("   OpenCV: [30, 170, 254] - [30, 230, 255]");
    println!("   Rust:   [{:.3}, {:.3}, {:.3}] - [{:.3}, {:.3}, {:.3}]", 
        yellow2_lower[0], yellow2_lower[1], yellow2_lower[2],
        yellow2_upper[0], yellow2_upper[1], yellow2_upper[2]);
    println!();
    
    // Purple
    let purple_lower = [144.0/179.0, 72.0/255.0, 150.0/255.0];
    let purple_upper = [152.0/179.0, 255.0/255.0, 255.0/255.0];
    println!("🟣 Purple:");
    println!("   OpenCV: [144, 72, 150] - [152, 255, 255]");
    println!("   Rust:   [{:.3}, {:.3}, {:.3}] - [{:.3}, {:.3}, {:.3}]", 
        purple_lower[0], purple_lower[1], purple_lower[2],
        purple_upper[0], purple_upper[1], purple_upper[2]);
    println!();
    
    // Anti Astra
    let astra_lower = [140.0/179.0, 86.0/255.0, 172.0/255.0];
    let astra_upper = [150.0/179.0, 255.0/255.0, 255.0/255.0];
    println!("🔮 Anti Astra:");
    println!("   OpenCV: [140, 86, 172] - [150, 255, 255]");
    println!("   Rust:   [{:.3}, {:.3}, {:.3}] - [{:.3}, {:.3}, {:.3}]", 
        astra_lower[0], astra_lower[1], astra_lower[2],
        astra_upper[0], astra_upper[1], astra_upper[2]);
    println!();
    
    // Red
    let red_lower = [0.0/179.0, 170.0/255.0, 150.0/255.0];
    let red_upper = [5.0/179.0, 255.0/255.0, 255.0/255.0];
    println!("🔴 Red:");
    println!("   OpenCV: [0, 170, 150] - [5, 255, 255]");
    println!("   Rust:   [{:.3}, {:.3}, {:.3}] - [{:.3}, {:.3}, {:.3}]", 
        red_lower[0], red_lower[1], red_lower[2],
        red_upper[0], red_upper[1], red_upper[2]);
    println!();
    
    println!("💡 HSV Color Space Quick Reference:");
    println!("   H (Hue): Color wheel position");
    println!("     0.0 = Red, 0.17 = Yellow, 0.33 = Green");
    println!("     0.5 = Cyan, 0.67 = Blue, 0.83 = Magenta");
    println!("   S (Saturation): Color intensity (0.0 = gray, 1.0 = vivid)");
    println!("   V (Value): Brightness (0.0 = black, 1.0 = bright)");
    println!();
    
    println!("🎯 To test colors:");
    println!("1. Run the capture application: cargo run --bin capture");
    println!("2. Select a color preset from the dropdown");
    println!("3. Enable 'Show Color Filter' to see detected areas");
    println!("4. For custom colors, select 'Custom' and adjust HSV sliders");
    println!();
    
    print!("Press Enter to exit...");
    io::stdout().flush().unwrap();
    let _ = io::stdin().read_line(&mut String::new());
}
